const path = require('path');

// Backend directories - use environment variables in production, fallback to local dirs in development
const PUBLIC_DIR = process.env.PUBLIC_DIR || path.join(__dirname, '..', '..', 'public');
const IMAGES_DIR = process.env.IMAGES_DIR || path.join(__dirname, '..', '..', 'images');
const ZIPS_DIR = process.env.ZIPS_DIR || path.join(__dirname, '..', '..', 'zips');

module.exports = {
    PUBLIC_DIR,
    IMAGES_DIR,
    ZIPS_DIR,
};