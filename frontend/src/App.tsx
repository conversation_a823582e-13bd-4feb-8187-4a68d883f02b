import { useState, useCallback, useEffect } from 'react';
import { io } from 'socket.io-client';
import { StatusPanel } from './components/StatusPanel';
import { ImageSection } from './components/ImageSection';
import { ControlPanel } from './components/ControlPanel';
import { LogViewer } from './components/LogViewer';
import { FileManagementPanel } from './components/FileManagementPanel';
import { ThemeToggle } from './components/ThemeToggle';
import { useTheme } from './hooks/useTheme';
import { useTimelapseSettings } from './hooks/useTimelapseSettings';
// import { mockLogs } from './data/mockData';
import type { Settings, TimelapseImage, LogEntry } from './types';
import { DEFAULT_TIMESTAMP_FORMAT } from './types';
import { DEFAULT_FIRST_IMAGE, DEFAULT_LAST_IMAGE } from './constants/assets';
import { LightDurationControls } from './components/setup/LightDurationControls'; // Import LightDurationControls

// Inherit the host and port the page was loaded from
// (nginx will proxy /socket.io to the Node server)
const socket = io({
  path: '/socket.io',
  timeout: 5000,
  reconnection: true,
  reconnectionAttempts: 5,
});

export default function App() {
  const { isDark, toggleTheme } = useTheme();
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [zipFiles, setZipFiles] = useState([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [logLevel, setLogLevel] = useState<'info' | 'warning' | 'error'>('warning');
  // Track connection status between frontend and backend server
  const [isConnected, setIsConnected] = useState(false);
  // Track video recording status
  const [isVideoRecording, setIsVideoRecording] = useState(false);
  const { settings, hasSettingsChanged, confirmSettingsChange, updateSettings } = useTimelapseSettings();
  const [isLight1Active, setIsLight1Active] = useState(false);
  const [isLight2Active, setIsLight2Active] = useState(false);
  const [isLight3Active, setIsLight3Active] = useState(false);
  // Use the default image objects from constants
  const [firstImage, setFirstImage] = useState<TimelapseImage>(DEFAULT_FIRST_IMAGE);
  const [lastImage, setLastImage] = useState<TimelapseImage>(DEFAULT_LAST_IMAGE);
  const {
    light1Connected,
    light2Connected,
    light3Connected,
    // timelapseActive,
    // timelapsePaused,
    isCameraConnected,
    // networkStatus is now derived from isConnected state
    imagesTaken,
    storage,
    availableImages,
    // zipFiles,
  } = settings;

  // Initialize connection state
  useEffect(() => {
    // We'll let the server tell us the actual connection states
    // Just set isConnected to false initially until we connect
    setIsConnected(false);

    // Handle successful connection to the backend server
    socket.on('connect', () => {
      console.log('Connected to backend server');
      setIsConnected(true); // Update connection status
      socket.emit('getStatus'); // Request initial status from backend
    });

    socket.on('status', (status) => {
      console.log('Status received:', status);
      console.log('Camera status:', status.isCameraConnected);
      // console.log('Light status values:', {
      //   light1: status.light1Status,
      //   light2: status.light2Status,
      //   light3: status.light3Status
      // });
      setIsRunning(status.timelapseActive);
      setIsPaused(status.timelapsePaused);
      setZipFiles(status.zipFiles);

      // Update camera connection status based on server response
      const cameraConnected = !!status.isCameraConnected;
      console.log('Setting camera connected:', cameraConnected);

      // Update light connection status from server
      const light1Connected = !!status.light1Connected;
      const light2Connected = !!status.light2Connected;
      const light3Connected = !!status.light3Connected;

      // Get light control modes
      const light1ControlMode = status.light1ControlMode || 'direct';
      const light2ControlMode = status.light2ControlMode || 'direct';
      const light3ControlMode = status.light3ControlMode || 'direct';

      // Get pulse widths
      const light1PulseWidth = status.light1PulseWidth || 100;
      const light2PulseWidth = status.light2PulseWidth || 100;
      const light3PulseWidth = status.light3PulseWidth || 100;

      // Get status known flags
      const light1StatusKnown = status.light1StatusKnown !== undefined ? !!status.light1StatusKnown : true;
      const light2StatusKnown = status.light2StatusKnown !== undefined ? !!status.light2StatusKnown : true;
      const light3StatusKnown = status.light3StatusKnown !== undefined ? !!status.light3StatusKnown : true;

      // Force update the settings immediately to ensure UI reflects correct state
      updateSettings({
        isCameraConnected: cameraConnected,
        light1Connected,
        light2Connected,
        light3Connected,
        light1ControlMode,
        light2ControlMode,
        light3ControlMode,
        light1PulseWidth,
        light2PulseWidth,
        light3PulseWidth,
        light1StatusKnown,
        light2StatusKnown,
        light3StatusKnown
      });

      // Update light active states
      setIsLight1Active(light1Connected ? !!status.light1Status : false);
      setIsLight2Active(light2Connected ? !!status.light2Status : false);
      setIsLight3Active(light3Connected ? !!status.light3Status : false);

      // Update first image if available from server
      if (status.firstImageUrl) {
        setFirstImage({
          url: status.firstImageUrl,
          timestamp: status.firstImageTimestamp
            ? new Date(status.firstImageTimestamp).toLocaleString()
            : DEFAULT_TIMESTAMP_FORMAT
        });
      }

      // Update last image if available from server
      if (status.lastImageUrl) {
        setLastImage({
          url: status.lastImageUrl,
          timestamp: status.lastImageTimestamp
            ? new Date(status.lastImageTimestamp).toLocaleString()
            : DEFAULT_TIMESTAMP_FORMAT
        });
      }

      // Debug log all status values
      console.log('Full status from server:', {
        isCameraConnected: cameraConnected,
        light1Connected,
        light2Connected,
        light3Connected,
        networkStatus: status.networkStatus,
        imagesTaken: status.imagesTaken,
        timelapseImagesTaken: status.timelapseImagesTaken,
        manualImagesTaken: status.manualImagesTaken
      });

      // Update settings with server values
      updateSettings({
        isCameraConnected: cameraConnected,
        networkStatus: status.networkStatus,
        light1Connected: light1Connected,
        light2Connected: light2Connected,
        light3Connected: light3Connected,
        imagesTaken: status.imagesTaken,
        timelapseImagesTaken: status.timelapseImagesTaken,
        manualImagesTaken: status.manualImagesTaken,
        storage: status.storage,
        availableImages: status.availableImages,
        // zipFiles: status.zipFiles,
        // DHT22 sensor data
        currentTemp: status.currentTemp,
        currentRH: status.currentRH,
        tempHistory: status.tempHistory,
        rhHistory: status.rhHistory
      });

    });

    // socket.on('lightStatus', (status) => {
    //   console.log('Light status:', status);
    //   // Only update light active states if the light is connected
    //   setIsLight1Active(() => light1Connected ? status.light1Status : false);
    //   setIsLight2Active(() => light2Connected ? status.light2Status : false);
    //   setIsLight3Active(() => light3Connected ? status.light3Status : false);
    // });

    socket.on('timelapseStatus', (status) => {
      console.log('Timelapse status:', status);
      setIsRunning(status.status === 'started');
      setIsPaused(status.status === 'paused');

      // Only reset counters in the frontend when timelapse is stopped
      if (status.status === 'started') {
        // Log current counters when timelapse is started (no reset)
        console.log('Starting timelapse - Keeping existing image counters');
        console.log(`Current counters: manual=${settings.manualImagesTaken}, timelapse=${settings.timelapseImagesTaken}, total=${settings.imagesTaken}`);
      } else if (status.status === 'stopped') {
        // Reset all counters when timelapse is stopped
        console.log('Resetting image counters in frontend due to timelapse stopped');
        updateSettings({
          imagesTaken: 0,
          timelapseImagesTaken: 0,
          manualImagesTaken: 0
        });
      }

      if (status.zipFilename) {
        handleDownloadZip(status.zipFilename);
      }
    });

    socket.on('zipCreated', (data) => {
      console.log('Zip created:', data.zipFilename);
      handleDownloadZip(data.zipFilename);
    });

    socket.on('zipDeleted', (data) => {
      console.log('Zip deleted:', data.filename);
      // Refresh the status to update the zip files list
      socket.emit('getStatus');
    });

    socket.on('serverLog', (logEntry: LogEntry) => {
      console.log('Server log:', logEntry);
      setLogs(prevLogs => {
        // Keep only the last 100 logs to prevent memory issues
        const newLogs = [...prevLogs, logEntry].slice(-100);
        return newLogs;
      });
    });

    socket.on('captureStatus', (status) => {
      console.log('Manual capture status:', status);
      if (status.status === 'captured') {
        // Use the counters sent from the server for manual captures
        updateSettings({
          imagesTaken: status.totalImagesTaken || (settings.imagesTaken || 0) + 1,
          manualImagesTaken: status.manualImagesTaken || (settings.manualImagesTaken || 0) + 1
        });
      }
    });

    // Handle timelapse capture events
    socket.on('timelapseCapture', (status) => {
      console.log('Timelapse capture status:', status);
      if (status.status === 'captured') {
        // Update the image counters
        updateSettings({
          imagesTaken: status.totalImagesTaken || (settings.imagesTaken || 0) + 1,
          timelapseImagesTaken: status.timelapseImagesTaken || (settings.timelapseImagesTaken || 0) + 1
        });

        // Request updated status to refresh the images
        socket.emit('getStatus');
      }
    });

    socket.on('videoStatus', (status) => {
      console.log('Video status:', status);
      // Update the recording state in the ControlPanel
      if (status.isRecording !== undefined) {
        // We need to pass this state to the ControlPanel
        setIsVideoRecording(status.isRecording);
      }
    });

    // Handle sensor updates from the DHT22 sensor
    socket.on('sensorUpdate', (data) => {
      console.log('Sensor update:', data);
      // Update the sensor data in the settings
      updateSettings({
        currentTemp: data.temperature,
        currentRH: data.humidity,
        tempHistory: data.tempHistory,
        rhHistory: data.rhHistory
      });
    });

    // Handle error messages from the server
    socket.on('error', (error) => {
      console.error('Server error:', error);
      alert(error.message || 'An error occurred');
    });

    // Handle disconnection from the backend server
    socket.on('disconnect', () => {
      console.log('Disconnected from backend server');
      setIsConnected(false); // Update connection status

      // Reset connection-dependent settings when backend is disconnected
      updateSettings({
        isCameraConnected: false,
        networkStatus: false,
        light1Connected: false,
        light2Connected: false,
        light3Connected: false
      });

      // Reset running state since we can't continue without backend
      setIsRunning(false);
      setIsPaused(false);
    });

    // Handle connection errors when trying to connect to the backend
    socket.on('connect_error', (error) => {
      console.error('Backend connection error:', error);
      setIsConnected(false); // Update connection status

      // Reset connection-dependent settings when backend is unreachable
      updateSettings({
        isCameraConnected: false,
        networkStatus: false,
        light1Connected: false,
        light2Connected: false,
        light3Connected: false
      });
    });

    return () => {
      socket.off('connect');
      socket.off('status');
      socket.off('timelapseStatus');
      socket.off('zipCreated');
      socket.off('zipDeleted');
      socket.off('serverLog');
      socket.off('captureStatus');
      socket.off('timelapseCapture'); // Remove the new event listener
      // socket.off('lightStatus');
      socket.off('videoStatus');
      socket.off('sensorUpdate'); // Remove the sensor update event listener
      socket.off('error'); // Remove the error event listener
      socket.off('disconnect');
      socket.off('connect_error');
    };
  } , [updateSettings]); // Only depend on updateSettings to avoid circular dependencies


  const handleLightConnectionChange = (lightIndex: number, lightConnected: boolean) => {
    // Only process if we're connected to the server
    if (!isConnected) {
      console.log('Cannot change light connection when server is disconnected');
      return;
    }

    // Convert from 0-indexed to 1-indexed for backend
    const lightNumber = lightIndex + 1;

    console.log(`Setting light ${lightNumber} connection to ${lightConnected}`);

    // Update local state
    const updates: Partial<Settings> = {};
    if (lightIndex === 0) {
      updates.light1Connected = lightConnected;
      if (!lightConnected) { updates.light1State = false; }
    } else if (lightIndex === 1) {
      updates.light2Connected = lightConnected;
      if (!lightConnected) { updates.light2State = false; }
    } else if (lightIndex === 2) {
      updates.light3Connected = lightConnected;
      if (!lightConnected) { updates.light3State = false; }
    }
    updateSettings(updates);

    // Send to backend for persistence
    socket.emit('setLightConnection', { lightNumber, isConnected: lightConnected });
  };

  const handleLightControlModeChange = (lightIndex: number, mode: 'direct' | 'toggle', pulseWidth?: number) => {
    // Only process if we're connected to the server
    if (!isConnected) {
      console.log('Cannot change light control mode when server is disconnected');
      return;
    }

    // Convert from 0-indexed to 1-indexed for backend
    const lightNumber = lightIndex + 1;

    console.log(`Setting light ${lightNumber} control mode to ${mode}${pulseWidth ? `, pulse width: ${pulseWidth}ms` : ''}`);

    // Update local state
    const updates: Partial<Settings> = {};
    if (lightIndex === 0) {
      updates.light1ControlMode = mode;
      if (pulseWidth) updates.light1PulseWidth = pulseWidth;
    } else if (lightIndex === 1) {
      updates.light2ControlMode = mode;
      if (pulseWidth) updates.light2PulseWidth = pulseWidth;
    } else if (lightIndex === 2) {
      updates.light3ControlMode = mode;
      if (pulseWidth) updates.light3PulseWidth = pulseWidth;
    }
    updateSettings(updates);

    // Send to backend for persistence
    socket.emit('updateLightControlMode', { lightNumber, mode, pulseWidth });
  };

  const handleConfirmLightStatus = (lightIndex: number, status: boolean) => {
    // Only process if we're connected to the server
    if (!isConnected) {
      console.log('Cannot confirm light status when server is disconnected');
      return;
    }

    // Convert from 0-indexed to 1-indexed for backend
    const lightNumber = lightIndex + 1;

    console.log(`Confirming light ${lightNumber} status as ${status ? 'ON' : 'OFF'}`);

    // Update local state
    const updates: Partial<Settings> = {};
    if (lightIndex === 0) {
      updates.light1State = status;
      updates.light1StatusKnown = true;
    } else if (lightIndex === 1) {
      updates.light2State = status;
      updates.light2StatusKnown = true;
    } else if (lightIndex === 2) {
      updates.light3State = status;
      updates.light3StatusKnown = true;
    }
    updateSettings(updates);

    // Send to backend for persistence
    socket.emit('confirmLightStatus', { lightNumber, status });
  };

  const handleSettingsChange = (settings: Settings) => {
    console.log('Settings updated:', settings);
  };

  const handleStart = useCallback(async () => {
    if (isPaused && hasSettingsChanged()) {
      const confirmed = await confirmSettingsChange();
      if (!confirmed) return;
    }

    // If paused, we're resuming the timelapse
    if (isPaused) {
      console.log('Resuming timelapse with frequency:', settings.frequency);
      socket.emit('resumeTimelapse', {
        frequency: settings.frequency
      });
    } else {
      // Otherwise, we're starting a new timelapse
      console.log('Emitting startTimelapse with settings:', settings);
      socket.emit('startTimelapse', {
        frequency: settings.frequency,
        // Pass light settings
        light1StartTime: settings.light1StartTime,
        light2StartTime: settings.light2StartTime,
        light3StartTime: settings.light3StartTime,
        light1EndTime: settings.light1EndTime,
        light2EndTime: settings.light2EndTime,
        light3EndTime: settings.light3EndTime,
        light1CaptureAction: settings.light1CaptureAction,
        light2CaptureAction: settings.light2CaptureAction,
        light3CaptureAction: settings.light3CaptureAction,
        light1Mode: settings.light1Mode,
        light2Mode: settings.light2Mode,
        light3Mode: settings.light3Mode
      });
    }
  }, [isPaused, hasSettingsChanged, confirmSettingsChange, settings]);

  const handlePause = () => {
    console.log('Emitting pauseTimelapse');
    socket.emit('pauseTimelapse');
  };

  const handleStop = () => {
    console.log('Emitting stopTimelapse');
    socket.emit('stopTimelapse');
  };

  const handleCapture = () => {
    console.log('Emitting captureImage');
    socket.emit('captureImage');
  };

  const handleFrequencyChange = (frequency: number) => {
    updateSettings({ frequency });
    handleSettingsChange({ ...settings, frequency });
  };

  const handleDownloadZip = async (filename: string) => {
    console.log('Downloading:', filename);

    // Extract host from socket URL
    // const socketUrl = new URL(socket.io.uri);
    // Since we initially connected to 'http://raspberrypi.local:3000', we'll use that
    const socketUrl = new URL('http://raspberrypi.local:3000');
    const baseUrl = `${socketUrl.protocol}//${socketUrl.host}`;

    const link = document.createElement('a');
    link.href = `${baseUrl}/download/${filename}`;
    link.download = filename;

    console.log(`Downloading from: ${link.href}`);

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCreateNewZip = async () => {
    console.log('Creating new zip');
    socket.emit('createZip');
  };

  const handleDeleteZip = async (filename: string) => {
    console.log('Deleting zip file:', filename);
    socket.emit('deleteZip', { filename });
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 transition-colors">
      <ThemeToggle isDark={isDark} onToggle={toggleTheme} />

      <div className="max-w-7xl mx-auto space-y-4">
        {/* Make grid fixed height and all columns h-full */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatusPanel
            data={{
              ...settings,
              networkStatus: isConnected, // Pass frontend-backend connection status
              storage: storage,
              imagesTaken,
              isCameraConnected: isCameraConnected,
              nextCapture: settings.nextCapture,
              light1State: isLight1Active,
              light2State: isLight2Active,
              light3State: isLight3Active,
              light1Connected: isConnected && light1Connected,
              light2Connected: isConnected && light2Connected,
              light3Connected: isConnected && light3Connected,
            }}
          />
          {/* Make middle column flex with full height */}
          <div className="h-full flex flex-col gap-4">
            <ImageSection
              firstImage={firstImage}
              lastImage={lastImage}
              isRunning={isRunning}
              isPaused={isPaused}
              onSettingsChange={handleSettingsChange}
            />
            {/* Make this div grow to fill remaining space */}
            <div className="flex-grow bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
              <h2 className="text-lg font-semibold dark:text-white mb-4">Light Settings</h2>
              <LightDurationControls
                disabled={!isConnected || (isRunning && !isPaused)}
                onLightConnectionChange={handleLightConnectionChange}
                onLightControlModeChange={handleLightControlModeChange}
                onConfirmLightStatus={handleConfirmLightStatus}
                onLightSettingsChange={(lightIndex, lightSettings) => {
                  // Update the light settings in the state
                  const updates: Partial<Settings> = {};
                  if (lightIndex === 0) {
                    if (lightSettings.startTime !== undefined) {
                      updates.light1StartTime = lightSettings.startTime;
                    }
                    if (lightSettings.endTime !== undefined) {
                      updates.light1EndTime = lightSettings.endTime;
                    }
                    if (lightSettings.captureAction !== undefined) {
                      updates.light1CaptureAction = lightSettings.captureAction;
                    }
                    if (lightSettings.mode !== undefined) {
                      updates.light1Mode = lightSettings.mode;
                    }
                  } else if (lightIndex === 1) {
                    if (lightSettings.startTime !== undefined) {
                      updates.light2StartTime = lightSettings.startTime;
                    }
                    if (lightSettings.endTime !== undefined) {
                      updates.light2EndTime = lightSettings.endTime;
                    }
                    if (lightSettings.captureAction !== undefined) {
                      updates.light2CaptureAction = lightSettings.captureAction;
                    }
                    if (lightSettings.mode !== undefined) {
                      updates.light2Mode = lightSettings.mode;
                    }
                  } else if (lightIndex === 2) {
                    if (lightSettings.startTime !== undefined) {
                      updates.light3StartTime = lightSettings.startTime;
                    }
                    if (lightSettings.endTime !== undefined) {
                      updates.light3EndTime = lightSettings.endTime;
                    }
                    if (lightSettings.captureAction !== undefined) {
                      updates.light3CaptureAction = lightSettings.captureAction;
                    }
                    if (lightSettings.mode !== undefined) {
                      updates.light3Mode = lightSettings.mode;
                    }
                  }

                  // Update the settings
                  updateSettings(updates);
                  console.log(`Updated light ${lightIndex + 1} settings:`, lightSettings);
                  handleSettingsChange({ ...settings, ...updates });
                }}
                light1Connected={isConnected && light1Connected}
                light2Connected={isConnected && light2Connected}
                light3Connected={isConnected && light3Connected}
                light1ControlMode={settings.light1ControlMode || 'direct'}
                light2ControlMode={settings.light2ControlMode || 'direct'}
                light3ControlMode={settings.light3ControlMode || 'direct'}
                light1PulseWidth={settings.light1PulseWidth || 100}
                light2PulseWidth={settings.light2PulseWidth || 100}
                light3PulseWidth={settings.light3PulseWidth || 100}
                light1StatusKnown={settings.light1StatusKnown !== false}
                light2StatusKnown={settings.light2StatusKnown !== false}
                light3StatusKnown={settings.light3StatusKnown !== false}
              />
            </div>
          </div>
          <ControlPanel
            onStart={handleStart}
            onPause={handlePause}
            onStop={handleStop}
            onCapture={handleCapture}
            onFrequencyChange={handleFrequencyChange}
            isRunning={isRunning}
            isPaused={isPaused}
            isCameraConnected={isCameraConnected}
            frequency={settings.frequency}
            isLight1Active={isLight1Active}
            isLight2Active={isLight2Active}
            isLight3Active={isLight3Active}
            light1Connected={isConnected && light1Connected}
            light2Connected={isConnected && light2Connected}
            light3Connected={isConnected && light3Connected}
            socket={socket}
            networkConnected={isConnected}
            isRecording={isVideoRecording}
          />
        </div>
        <FileManagementPanel
          zipFiles={zipFiles}
          hasUnzippedImages={availableImages > 0}
          unzippedImagesCount={availableImages}
          onDownloadZip={handleDownloadZip}
          onCreateNewZip={handleCreateNewZip}
          onDeleteZip={handleDeleteZip}
        />

        <LogViewer
          logs={logs}
          logLevel={logLevel}
          onLogLevelChange={setLogLevel}
          isExpanded={settings.logsExpanded}
          onToggle={() => updateSettings({ logsExpanded: !settings.logsExpanded })}
        />
      </div>
    </div>
  );
}