{"version": 3, "names": ["_is", "require", "_validate", "VISITOR_KEYS", "exports", "ALIAS_KEYS", "FLIPPED_ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "NODE_PARENT_VALIDATIONS", "getType", "val", "Array", "isArray", "validate", "validateType", "typeNames", "assertNodeType", "validateOptional", "optional", "validateOptionalType", "arrayOf", "elementType", "chain", "assertValueType", "assertEach", "arrayOfType", "validateArrayOfType", "callback", "child<PERSON><PERSON><PERSON><PERSON>", "process", "env", "BABEL_TYPES_8_BREAKING", "validate<PERSON><PERSON><PERSON>", "validator", "node", "key", "i", "length", "subkey", "v", "each", "assertOneOf", "values", "includes", "TypeError", "JSON", "stringify", "oneOf", "types", "type", "is", "oneOfNodeTypes", "assertNodeOrValueType", "oneOfNodeOrValueTypes", "valid", "assertShape", "shape", "errors", "property", "Object", "keys", "validateField", "error", "push", "message", "join", "shapeOf", "assertOptionalChainStart", "_current", "current", "callee", "object", "fns", "args", "fn", "chainOf", "Error", "validTypeOpts", "Set", "valid<PERSON>ield<PERSON>eys", "store", "defineAliasedType", "aliases", "opts", "defined", "_store$opts$inherits$", "_defined", "inherits", "slice", "additional", "filter", "a", "unshift", "defineType", "fields", "getOwnPropertyNames", "field", "def", "default", "deprecated", "visitor", "builder", "k", "has", "depre<PERSON><PERSON><PERSON><PERSON>", "concat", "undefined", "for<PERSON>ach", "alias"], "sources": ["../../src/definitions/utils.ts"], "sourcesContent": ["import is from \"../validators/is.ts\";\nimport { validateField, validateChild } from \"../validators/validate.ts\";\nimport type * as t from \"../index.ts\";\n\nexport const VISITOR_KEYS: Record<string, string[]> = {};\nexport const ALIAS_KEYS: Partial<Record<NodeTypesWithoutComment, string[]>> =\n  {};\nexport const FLIPPED_ALIAS_KEYS: Record<string, NodeTypesWithoutComment[]> = {};\nexport const NODE_FIELDS: Record<string, FieldDefinitions> = {};\nexport const BUILDER_KEYS: Record<string, string[]> = {};\nexport const DEPRECATED_KEYS: Record<string, NodeTypesWithoutComment> = {};\nexport const NODE_PARENT_VALIDATIONS: Record<string, Validator> = {};\n\nfunction getType(val: any) {\n  if (Array.isArray(val)) {\n    return \"array\";\n  } else if (val === null) {\n    return \"null\";\n  } else {\n    return typeof val;\n  }\n}\n\ntype NodeTypesWithoutComment = t.Node[\"type\"] | keyof t.Aliases;\n\ntype NodeTypes = NodeTypesWithoutComment | t.Comment[\"type\"];\n\ntype PrimitiveTypes = ReturnType<typeof getType>;\n\ntype FieldDefinitions = {\n  [x: string]: FieldOptions;\n};\n\ntype DefineTypeOpts = {\n  fields?: FieldDefinitions;\n  visitor?: Array<string>;\n  aliases?: Array<string>;\n  builder?: Array<string>;\n  inherits?: NodeTypes;\n  deprecatedAlias?: string;\n  validate?: Validator;\n};\n\nexport type Validator = (\n  | { type: PrimitiveTypes }\n  | { each: Validator }\n  | { chainOf: Validator[] }\n  | { oneOf: any[] }\n  | { oneOfNodeTypes: NodeTypes[] }\n  | { oneOfNodeOrValueTypes: (NodeTypes | PrimitiveTypes)[] }\n  | { shapeOf: { [x: string]: FieldOptions } }\n  | object\n) &\n  ((node: t.Node, key: string, val: any) => void);\n\nexport type FieldOptions = {\n  default?: string | number | boolean | [];\n  optional?: boolean;\n  deprecated?: boolean;\n  validate?: Validator;\n};\n\nexport function validate(validate: Validator): FieldOptions {\n  return { validate };\n}\n\nexport function validateType(...typeNames: NodeTypes[]) {\n  return validate(assertNodeType(...typeNames));\n}\n\nexport function validateOptional(validate: Validator): FieldOptions {\n  return { validate, optional: true };\n}\n\nexport function validateOptionalType(...typeNames: NodeTypes[]): FieldOptions {\n  return { validate: assertNodeType(...typeNames), optional: true };\n}\n\nexport function arrayOf(elementType: Validator): Validator {\n  return chain(assertValueType(\"array\"), assertEach(elementType));\n}\n\nexport function arrayOfType(...typeNames: NodeTypes[]) {\n  return arrayOf(assertNodeType(...typeNames));\n}\n\nexport function validateArrayOfType(...typeNames: NodeTypes[]) {\n  return validate(arrayOfType(...typeNames));\n}\n\nexport function assertEach(callback: Validator): Validator {\n  const childValidator = process.env.BABEL_TYPES_8_BREAKING\n    ? validateChild\n    : () => {};\n\n  function validator(node: t.Node, key: string, val: any) {\n    if (!Array.isArray(val)) return;\n\n    for (let i = 0; i < val.length; i++) {\n      const subkey = `${key}[${i}]`;\n      const v = val[i];\n      callback(node, subkey, v);\n      childValidator(node, subkey, v);\n    }\n  }\n  validator.each = callback;\n  return validator;\n}\n\nexport function assertOneOf(...values: Array<any>): Validator {\n  function validate(node: any, key: string, val: any) {\n    if (!values.includes(val)) {\n      throw new TypeError(\n        `Property ${key} expected value to be one of ${JSON.stringify(\n          values,\n        )} but got ${JSON.stringify(val)}`,\n      );\n    }\n  }\n\n  validate.oneOf = values;\n\n  return validate;\n}\n\nexport function assertNodeType(...types: NodeTypes[]): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeTypes = types;\n\n  return validate;\n}\n\nexport function assertNodeOrValueType(\n  ...types: (NodeTypes | PrimitiveTypes)[]\n): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (getType(val) === type || is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeOrValueTypes = types;\n\n  return validate;\n}\n\nexport function assertValueType(type: PrimitiveTypes): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const valid = getType(val) === type;\n\n    if (!valid) {\n      throw new TypeError(\n        `Property ${key} expected type of ${type} but got ${getType(val)}`,\n      );\n    }\n  }\n\n  validate.type = type;\n\n  return validate;\n}\n\nexport function assertShape(shape: { [x: string]: FieldOptions }): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const errors = [];\n    for (const property of Object.keys(shape)) {\n      try {\n        validateField(node, property, val[property], shape[property]);\n      } catch (error) {\n        if (error instanceof TypeError) {\n          errors.push(error.message);\n          continue;\n        }\n        throw error;\n      }\n    }\n    if (errors.length) {\n      throw new TypeError(\n        `Property ${key} of ${\n          node.type\n        } expected to have the following:\\n${errors.join(\"\\n\")}`,\n      );\n    }\n  }\n\n  validate.shapeOf = shape;\n\n  return validate;\n}\n\nexport function assertOptionalChainStart(): Validator {\n  function validate(node: t.Node) {\n    let current = node;\n    while (node) {\n      const { type } = current;\n      if (type === \"OptionalCallExpression\") {\n        if (current.optional) return;\n        current = current.callee;\n        continue;\n      }\n\n      if (type === \"OptionalMemberExpression\") {\n        if (current.optional) return;\n        current = current.object;\n        continue;\n      }\n\n      break;\n    }\n\n    throw new TypeError(\n      `Non-optional ${node.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${current?.type}`,\n    );\n  }\n\n  return validate;\n}\n\nexport function chain(...fns: Array<Validator>): Validator {\n  function validate(...args: Parameters<Validator>) {\n    for (const fn of fns) {\n      fn(...args);\n    }\n  }\n  validate.chainOf = fns;\n\n  if (\n    fns.length >= 2 &&\n    \"type\" in fns[0] &&\n    fns[0].type === \"array\" &&\n    !(\"each\" in fns[1])\n  ) {\n    throw new Error(\n      `An assertValueType(\"array\") validator can only be followed by an assertEach(...) validator.`,\n    );\n  }\n\n  return validate;\n}\n\nconst validTypeOpts = new Set([\n  \"aliases\",\n  \"builder\",\n  \"deprecatedAlias\",\n  \"fields\",\n  \"inherits\",\n  \"visitor\",\n  \"validate\",\n]);\nconst validFieldKeys = new Set([\n  \"default\",\n  \"optional\",\n  \"deprecated\",\n  \"validate\",\n]);\n\nconst store = {} as Record<string, DefineTypeOpts>;\n\n// Wraps defineType to ensure these aliases are included.\nexport function defineAliasedType(...aliases: string[]) {\n  return (type: string, opts: DefineTypeOpts = {}) => {\n    let defined = opts.aliases;\n    if (!defined) {\n      if (opts.inherits) defined = store[opts.inherits].aliases?.slice();\n      defined ??= [];\n      opts.aliases = defined;\n    }\n    const additional = aliases.filter(a => !defined.includes(a));\n    defined.unshift(...additional);\n    defineType(type, opts);\n  };\n}\n\nexport default function defineType(type: string, opts: DefineTypeOpts = {}) {\n  const inherits = (opts.inherits && store[opts.inherits]) || {};\n\n  let fields = opts.fields;\n  if (!fields) {\n    fields = {};\n    if (inherits.fields) {\n      const keys = Object.getOwnPropertyNames(inherits.fields);\n      for (const key of keys) {\n        const field = inherits.fields[key];\n        const def = field.default;\n        if (\n          Array.isArray(def) ? def.length > 0 : def && typeof def === \"object\"\n        ) {\n          throw new Error(\n            \"field defaults can only be primitives or empty arrays currently\",\n          );\n        }\n        fields[key] = {\n          default: Array.isArray(def) ? [] : def,\n          optional: field.optional,\n          deprecated: field.deprecated,\n          validate: field.validate,\n        };\n      }\n    }\n  }\n\n  const visitor: Array<string> = opts.visitor || inherits.visitor || [];\n  const aliases: Array<string> = opts.aliases || inherits.aliases || [];\n  const builder: Array<string> =\n    opts.builder || inherits.builder || opts.visitor || [];\n\n  for (const k of Object.keys(opts)) {\n    if (!validTypeOpts.has(k)) {\n      throw new Error(`Unknown type option \"${k}\" on ${type}`);\n    }\n  }\n\n  if (opts.deprecatedAlias) {\n    DEPRECATED_KEYS[opts.deprecatedAlias] = type as NodeTypesWithoutComment;\n  }\n\n  // ensure all field keys are represented in `fields`\n  for (const key of visitor.concat(builder)) {\n    fields[key] = fields[key] || {};\n  }\n\n  for (const key of Object.keys(fields)) {\n    const field = fields[key];\n\n    if (field.default !== undefined && !builder.includes(key)) {\n      field.optional = true;\n    }\n    if (field.default === undefined) {\n      field.default = null;\n    } else if (!field.validate && field.default != null) {\n      field.validate = assertValueType(getType(field.default));\n    }\n\n    for (const k of Object.keys(field)) {\n      if (!validFieldKeys.has(k)) {\n        throw new Error(`Unknown field key \"${k}\" on ${type}.${key}`);\n      }\n    }\n  }\n\n  VISITOR_KEYS[type] = opts.visitor = visitor;\n  BUILDER_KEYS[type] = opts.builder = builder;\n  NODE_FIELDS[type] = opts.fields = fields;\n  ALIAS_KEYS[type as NodeTypesWithoutComment] = opts.aliases = aliases;\n  aliases.forEach(alias => {\n    FLIPPED_ALIAS_KEYS[alias] = FLIPPED_ALIAS_KEYS[alias] || [];\n    FLIPPED_ALIAS_KEYS[alias].push(type as NodeTypesWithoutComment);\n  });\n\n  if (opts.validate) {\n    NODE_PARENT_VALIDATIONS[type] = opts.validate;\n  }\n\n  store[type] = opts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAGO,MAAME,YAAsC,GAAAC,OAAA,CAAAD,YAAA,GAAG,CAAC,CAAC;AACjD,MAAME,UAA8D,GAAAD,OAAA,CAAAC,UAAA,GACzE,CAAC,CAAC;AACG,MAAMC,kBAA6D,GAAAF,OAAA,CAAAE,kBAAA,GAAG,CAAC,CAAC;AACxE,MAAMC,WAA6C,GAAAH,OAAA,CAAAG,WAAA,GAAG,CAAC,CAAC;AACxD,MAAMC,YAAsC,GAAAJ,OAAA,CAAAI,YAAA,GAAG,CAAC,CAAC;AACjD,MAAMC,eAAwD,GAAAL,OAAA,CAAAK,eAAA,GAAG,CAAC,CAAC;AACnE,MAAMC,uBAAkD,GAAAN,OAAA,CAAAM,uBAAA,GAAG,CAAC,CAAC;AAEpE,SAASC,OAAOA,CAACC,GAAQ,EAAE;EACzB,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACtB,OAAO,OAAO;EAChB,CAAC,MAAM,IAAIA,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,MAAM;EACf,CAAC,MAAM;IACL,OAAO,OAAOA,GAAG;EACnB;AACF;AAyCO,SAASG,QAAQA,CAACA,QAAmB,EAAgB;EAC1D,OAAO;IAAEA;EAAS,CAAC;AACrB;AAEO,SAASC,YAAYA,CAAC,GAAGC,SAAsB,EAAE;EACtD,OAAOF,QAAQ,CAACG,cAAc,CAAC,GAAGD,SAAS,CAAC,CAAC;AAC/C;AAEO,SAASE,gBAAgBA,CAACJ,QAAmB,EAAgB;EAClE,OAAO;IAAEA,QAAQ;IAAEK,QAAQ,EAAE;EAAK,CAAC;AACrC;AAEO,SAASC,oBAAoBA,CAAC,GAAGJ,SAAsB,EAAgB;EAC5E,OAAO;IAAEF,QAAQ,EAAEG,cAAc,CAAC,GAAGD,SAAS,CAAC;IAAEG,QAAQ,EAAE;EAAK,CAAC;AACnE;AAEO,SAASE,OAAOA,CAACC,WAAsB,EAAa;EACzD,OAAOC,KAAK,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEC,UAAU,CAACH,WAAW,CAAC,CAAC;AACjE;AAEO,SAASI,WAAWA,CAAC,GAAGV,SAAsB,EAAE;EACrD,OAAOK,OAAO,CAACJ,cAAc,CAAC,GAAGD,SAAS,CAAC,CAAC;AAC9C;AAEO,SAASW,mBAAmBA,CAAC,GAAGX,SAAsB,EAAE;EAC7D,OAAOF,QAAQ,CAACY,WAAW,CAAC,GAAGV,SAAS,CAAC,CAAC;AAC5C;AAEO,SAASS,UAAUA,CAACG,QAAmB,EAAa;EACzD,MAAMC,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACrDC,uBAAa,GACb,MAAM,CAAC,CAAC;EAEZ,SAASC,SAASA,CAACC,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACtD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IAEzB,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,GAAG,CAAC2B,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,MAAME,MAAM,GAAG,GAAGH,GAAG,IAAIC,CAAC,GAAG;MAC7B,MAAMG,CAAC,GAAG7B,GAAG,CAAC0B,CAAC,CAAC;MAChBT,QAAQ,CAACO,IAAI,EAAEI,MAAM,EAAEC,CAAC,CAAC;MACzBX,cAAc,CAACM,IAAI,EAAEI,MAAM,EAAEC,CAAC,CAAC;IACjC;EACF;EACAN,SAAS,CAACO,IAAI,GAAGb,QAAQ;EACzB,OAAOM,SAAS;AAClB;AAEO,SAASQ,WAAWA,CAAC,GAAGC,MAAkB,EAAa;EAC5D,SAAS7B,QAAQA,CAACqB,IAAS,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IAClD,IAAI,CAACgC,MAAM,CAACC,QAAQ,CAACjC,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIkC,SAAS,CACjB,YAAYT,GAAG,gCAAgCU,IAAI,CAACC,SAAS,CAC3DJ,MACF,CAAC,YAAYG,IAAI,CAACC,SAAS,CAACpC,GAAG,CAAC,EAClC,CAAC;IACH;EACF;EAEAG,QAAQ,CAACkC,KAAK,GAAGL,MAAM;EAEvB,OAAO7B,QAAQ;AACjB;AAEO,SAASG,cAAcA,CAAC,GAAGgC,KAAkB,EAAa;EAC/D,SAASnC,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,KAAK,MAAMuC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAI,IAAAE,WAAE,EAACD,IAAI,EAAEvC,GAAG,CAAC,EAAE;QACjB,IAAAsB,uBAAa,EAACE,IAAI,EAAEC,GAAG,EAAEzB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA,MAAM,IAAIkC,SAAS,CACjB,YAAYT,GAAG,OACbD,IAAI,CAACe,IAAI,kCACuBJ,IAAI,CAACC,SAAS,CAC9CE,KACF,CAAC,oBAAoBH,IAAI,CAACC,SAAS,CAACpC,GAAG,oBAAHA,GAAG,CAAEuC,IAAI,CAAC,EAChD,CAAC;EACH;EAEApC,QAAQ,CAACsC,cAAc,GAAGH,KAAK;EAE/B,OAAOnC,QAAQ;AACjB;AAEO,SAASuC,qBAAqBA,CACnC,GAAGJ,KAAqC,EAC7B;EACX,SAASnC,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,KAAK,MAAMuC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIvC,OAAO,CAACC,GAAG,CAAC,KAAKuC,IAAI,IAAI,IAAAC,WAAE,EAACD,IAAI,EAAEvC,GAAG,CAAC,EAAE;QAC1C,IAAAsB,uBAAa,EAACE,IAAI,EAAEC,GAAG,EAAEzB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA,MAAM,IAAIkC,SAAS,CACjB,YAAYT,GAAG,OACbD,IAAI,CAACe,IAAI,kCACuBJ,IAAI,CAACC,SAAS,CAC9CE,KACF,CAAC,oBAAoBH,IAAI,CAACC,SAAS,CAACpC,GAAG,oBAAHA,GAAG,CAAEuC,IAAI,CAAC,EAChD,CAAC;EACH;EAEApC,QAAQ,CAACwC,qBAAqB,GAAGL,KAAK;EAEtC,OAAOnC,QAAQ;AACjB;AAEO,SAASU,eAAeA,CAAC0B,IAAoB,EAAa;EAC/D,SAASpC,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,MAAM4C,KAAK,GAAG7C,OAAO,CAACC,GAAG,CAAC,KAAKuC,IAAI;IAEnC,IAAI,CAACK,KAAK,EAAE;MACV,MAAM,IAAIV,SAAS,CACjB,YAAYT,GAAG,qBAAqBc,IAAI,YAAYxC,OAAO,CAACC,GAAG,CAAC,EAClE,CAAC;IACH;EACF;EAEAG,QAAQ,CAACoC,IAAI,GAAGA,IAAI;EAEpB,OAAOpC,QAAQ;AACjB;AAEO,SAAS0C,WAAWA,CAACC,KAAoC,EAAa;EAC3E,SAAS3C,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,MAAM+C,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMC,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;MACzC,IAAI;QACF,IAAAK,uBAAa,EAAC3B,IAAI,EAAEwB,QAAQ,EAAEhD,GAAG,CAACgD,QAAQ,CAAC,EAAEF,KAAK,CAACE,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IAAIA,KAAK,YAAYlB,SAAS,EAAE;UAC9Ba,MAAM,CAACM,IAAI,CAACD,KAAK,CAACE,OAAO,CAAC;UAC1B;QACF;QACA,MAAMF,KAAK;MACb;IACF;IACA,IAAIL,MAAM,CAACpB,MAAM,EAAE;MACjB,MAAM,IAAIO,SAAS,CACjB,YAAYT,GAAG,OACbD,IAAI,CAACe,IAAI,qCAC0BQ,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,EACxD,CAAC;IACH;EACF;EAEApD,QAAQ,CAACqD,OAAO,GAAGV,KAAK;EAExB,OAAO3C,QAAQ;AACjB;AAEO,SAASsD,wBAAwBA,CAAA,EAAc;EACpD,SAAStD,QAAQA,CAACqB,IAAY,EAAE;IAAA,IAAAkC,QAAA;IAC9B,IAAIC,OAAO,GAAGnC,IAAI;IAClB,OAAOA,IAAI,EAAE;MACX,MAAM;QAAEe;MAAK,CAAC,GAAGoB,OAAO;MACxB,IAAIpB,IAAI,KAAK,wBAAwB,EAAE;QACrC,IAAIoB,OAAO,CAACnD,QAAQ,EAAE;QACtBmD,OAAO,GAAGA,OAAO,CAACC,MAAM;QACxB;MACF;MAEA,IAAIrB,IAAI,KAAK,0BAA0B,EAAE;QACvC,IAAIoB,OAAO,CAACnD,QAAQ,EAAE;QACtBmD,OAAO,GAAGA,OAAO,CAACE,MAAM;QACxB;MACF;MAEA;IACF;IAEA,MAAM,IAAI3B,SAAS,CACjB,gBAAgBV,IAAI,CAACe,IAAI,sGAAAmB,QAAA,GAAqGC,OAAO,qBAAPD,QAAA,CAASnB,IAAI,EAC7I,CAAC;EACH;EAEA,OAAOpC,QAAQ;AACjB;AAEO,SAASS,KAAKA,CAAC,GAAGkD,GAAqB,EAAa;EACzD,SAAS3D,QAAQA,CAAC,GAAG4D,IAA2B,EAAE;IAChD,KAAK,MAAMC,EAAE,IAAIF,GAAG,EAAE;MACpBE,EAAE,CAAC,GAAGD,IAAI,CAAC;IACb;EACF;EACA5D,QAAQ,CAAC8D,OAAO,GAAGH,GAAG;EAEtB,IACEA,GAAG,CAACnC,MAAM,IAAI,CAAC,IACf,MAAM,IAAImC,GAAG,CAAC,CAAC,CAAC,IAChBA,GAAG,CAAC,CAAC,CAAC,CAACvB,IAAI,KAAK,OAAO,IACvB,EAAE,MAAM,IAAIuB,GAAG,CAAC,CAAC,CAAC,CAAC,EACnB;IACA,MAAM,IAAII,KAAK,CACb,6FACF,CAAC;EACH;EAEA,OAAO/D,QAAQ;AACjB;AAEA,MAAMgE,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC5B,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,CACX,CAAC;AACF,MAAMC,cAAc,GAAG,IAAID,GAAG,CAAC,CAC7B,SAAS,EACT,UAAU,EACV,YAAY,EACZ,UAAU,CACX,CAAC;AAEF,MAAME,KAAK,GAAG,CAAC,CAAmC;AAG3C,SAASC,iBAAiBA,CAAC,GAAGC,OAAiB,EAAE;EACtD,OAAO,CAACjC,IAAY,EAAEkC,IAAoB,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIC,OAAO,GAAGD,IAAI,CAACD,OAAO;IAC1B,IAAI,CAACE,OAAO,EAAE;MAAA,IAAAC,qBAAA,EAAAC,QAAA;MACZ,IAAIH,IAAI,CAACI,QAAQ,EAAEH,OAAO,IAAAC,qBAAA,GAAGL,KAAK,CAACG,IAAI,CAACI,QAAQ,CAAC,CAACL,OAAO,qBAA5BG,qBAAA,CAA8BG,KAAK,CAAC,CAAC;MAClE,CAAAF,QAAA,GAAAF,OAAO,YAAAE,QAAA,GAAPF,OAAO,GAAK,EAAE;MACdD,IAAI,CAACD,OAAO,GAAGE,OAAO;IACxB;IACA,MAAMK,UAAU,GAAGP,OAAO,CAACQ,MAAM,CAACC,CAAC,IAAI,CAACP,OAAO,CAACzC,QAAQ,CAACgD,CAAC,CAAC,CAAC;IAC5DP,OAAO,CAACQ,OAAO,CAAC,GAAGH,UAAU,CAAC;IAC9BI,UAAU,CAAC5C,IAAI,EAAEkC,IAAI,CAAC;EACxB,CAAC;AACH;AAEe,SAASU,UAAUA,CAAC5C,IAAY,EAAEkC,IAAoB,GAAG,CAAC,CAAC,EAAE;EAC1E,MAAMI,QAAQ,GAAIJ,IAAI,CAACI,QAAQ,IAAIP,KAAK,CAACG,IAAI,CAACI,QAAQ,CAAC,IAAK,CAAC,CAAC;EAE9D,IAAIO,MAAM,GAAGX,IAAI,CAACW,MAAM;EACxB,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAG,CAAC,CAAC;IACX,IAAIP,QAAQ,CAACO,MAAM,EAAE;MACnB,MAAMlC,IAAI,GAAGD,MAAM,CAACoC,mBAAmB,CAACR,QAAQ,CAACO,MAAM,CAAC;MACxD,KAAK,MAAM3D,GAAG,IAAIyB,IAAI,EAAE;QACtB,MAAMoC,KAAK,GAAGT,QAAQ,CAACO,MAAM,CAAC3D,GAAG,CAAC;QAClC,MAAM8D,GAAG,GAAGD,KAAK,CAACE,OAAO;QACzB,IACEvF,KAAK,CAACC,OAAO,CAACqF,GAAG,CAAC,GAAGA,GAAG,CAAC5D,MAAM,GAAG,CAAC,GAAG4D,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACpE;UACA,MAAM,IAAIrB,KAAK,CACb,iEACF,CAAC;QACH;QACAkB,MAAM,CAAC3D,GAAG,CAAC,GAAG;UACZ+D,OAAO,EAAEvF,KAAK,CAACC,OAAO,CAACqF,GAAG,CAAC,GAAG,EAAE,GAAGA,GAAG;UACtC/E,QAAQ,EAAE8E,KAAK,CAAC9E,QAAQ;UACxBiF,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BtF,QAAQ,EAAEmF,KAAK,CAACnF;QAClB,CAAC;MACH;IACF;EACF;EAEA,MAAMuF,OAAsB,GAAGjB,IAAI,CAACiB,OAAO,IAAIb,QAAQ,CAACa,OAAO,IAAI,EAAE;EACrE,MAAMlB,OAAsB,GAAGC,IAAI,CAACD,OAAO,IAAIK,QAAQ,CAACL,OAAO,IAAI,EAAE;EACrE,MAAMmB,OAAsB,GAC1BlB,IAAI,CAACkB,OAAO,IAAId,QAAQ,CAACc,OAAO,IAAIlB,IAAI,CAACiB,OAAO,IAAI,EAAE;EAExD,KAAK,MAAME,CAAC,IAAI3C,MAAM,CAACC,IAAI,CAACuB,IAAI,CAAC,EAAE;IACjC,IAAI,CAACN,aAAa,CAAC0B,GAAG,CAACD,CAAC,CAAC,EAAE;MACzB,MAAM,IAAI1B,KAAK,CAAC,wBAAwB0B,CAAC,QAAQrD,IAAI,EAAE,CAAC;IAC1D;EACF;EAEA,IAAIkC,IAAI,CAACqB,eAAe,EAAE;IACxBjG,eAAe,CAAC4E,IAAI,CAACqB,eAAe,CAAC,GAAGvD,IAA+B;EACzE;EAGA,KAAK,MAAMd,GAAG,IAAIiE,OAAO,CAACK,MAAM,CAACJ,OAAO,CAAC,EAAE;IACzCP,MAAM,CAAC3D,GAAG,CAAC,GAAG2D,MAAM,CAAC3D,GAAG,CAAC,IAAI,CAAC,CAAC;EACjC;EAEA,KAAK,MAAMA,GAAG,IAAIwB,MAAM,CAACC,IAAI,CAACkC,MAAM,CAAC,EAAE;IACrC,MAAME,KAAK,GAAGF,MAAM,CAAC3D,GAAG,CAAC;IAEzB,IAAI6D,KAAK,CAACE,OAAO,KAAKQ,SAAS,IAAI,CAACL,OAAO,CAAC1D,QAAQ,CAACR,GAAG,CAAC,EAAE;MACzD6D,KAAK,CAAC9E,QAAQ,GAAG,IAAI;IACvB;IACA,IAAI8E,KAAK,CAACE,OAAO,KAAKQ,SAAS,EAAE;MAC/BV,KAAK,CAACE,OAAO,GAAG,IAAI;IACtB,CAAC,MAAM,IAAI,CAACF,KAAK,CAACnF,QAAQ,IAAImF,KAAK,CAACE,OAAO,IAAI,IAAI,EAAE;MACnDF,KAAK,CAACnF,QAAQ,GAAGU,eAAe,CAACd,OAAO,CAACuF,KAAK,CAACE,OAAO,CAAC,CAAC;IAC1D;IAEA,KAAK,MAAMI,CAAC,IAAI3C,MAAM,CAACC,IAAI,CAACoC,KAAK,CAAC,EAAE;MAClC,IAAI,CAACjB,cAAc,CAACwB,GAAG,CAACD,CAAC,CAAC,EAAE;QAC1B,MAAM,IAAI1B,KAAK,CAAC,sBAAsB0B,CAAC,QAAQrD,IAAI,IAAId,GAAG,EAAE,CAAC;MAC/D;IACF;EACF;EAEAlC,YAAY,CAACgD,IAAI,CAAC,GAAGkC,IAAI,CAACiB,OAAO,GAAGA,OAAO;EAC3C9F,YAAY,CAAC2C,IAAI,CAAC,GAAGkC,IAAI,CAACkB,OAAO,GAAGA,OAAO;EAC3ChG,WAAW,CAAC4C,IAAI,CAAC,GAAGkC,IAAI,CAACW,MAAM,GAAGA,MAAM;EACxC3F,UAAU,CAAC8C,IAAI,CAA4B,GAAGkC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACpEA,OAAO,CAACyB,OAAO,CAACC,KAAK,IAAI;IACvBxG,kBAAkB,CAACwG,KAAK,CAAC,GAAGxG,kBAAkB,CAACwG,KAAK,CAAC,IAAI,EAAE;IAC3DxG,kBAAkB,CAACwG,KAAK,CAAC,CAAC7C,IAAI,CAACd,IAA+B,CAAC;EACjE,CAAC,CAAC;EAEF,IAAIkC,IAAI,CAACtE,QAAQ,EAAE;IACjBL,uBAAuB,CAACyC,IAAI,CAAC,GAAGkC,IAAI,CAACtE,QAAQ;EAC/C;EAEAmE,KAAK,CAAC/B,IAAI,CAAC,GAAGkC,IAAI;AACpB", "ignoreList": []}