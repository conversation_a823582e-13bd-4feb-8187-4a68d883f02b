{"version": 3, "names": ["_index", "require"], "sources": ["../../../src/builders/generated/uppercase.js"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\n\n/**\n * This file is written in JavaScript and not TypeScript because uppercase builders\n * conflict with AST types. TypeScript reads the uppercase.d.ts file instead.\n */\n\nexport {\n  arrayExpression as ArrayExpression,\n  assignmentExpression as AssignmentExpression,\n  binaryExpression as BinaryExpression,\n  interpreterDirective as InterpreterDirective,\n  directive as Directive,\n  directiveLiteral as DirectiveLiteral,\n  blockStatement as BlockStatement,\n  breakStatement as BreakStatement,\n  callExpression as CallExpression,\n  catchClause as CatchClause,\n  conditionalExpression as ConditionalExpression,\n  continueStatement as ContinueStatement,\n  debuggerStatement as DebuggerStatement,\n  doWhileStatement as DoWhileStatement,\n  emptyStatement as EmptyStatement,\n  expressionStatement as ExpressionStatement,\n  file as File,\n  forInStatement as ForInStatement,\n  forStatement as ForStatement,\n  functionDeclaration as FunctionDeclaration,\n  functionExpression as FunctionExpression,\n  identifier as Identifier,\n  ifStatement as IfStatement,\n  labeledStatement as LabeledStatement,\n  stringLiteral as StringLiteral,\n  numericLiteral as NumericLiteral,\n  nullLiteral as NullLiteral,\n  booleanLiteral as BooleanLiteral,\n  regExpLiteral as RegExpLiteral,\n  logicalExpression as LogicalExpression,\n  memberExpression as MemberExpression,\n  newExpression as NewExpression,\n  program as Program,\n  objectExpression as ObjectExpression,\n  objectMethod as ObjectMethod,\n  objectProperty as ObjectProperty,\n  restElement as RestElement,\n  returnStatement as ReturnStatement,\n  sequenceExpression as SequenceExpression,\n  parenthesizedExpression as ParenthesizedExpression,\n  switchCase as SwitchCase,\n  switchStatement as SwitchStatement,\n  thisExpression as ThisExpression,\n  throwStatement as ThrowStatement,\n  tryStatement as TryStatement,\n  unaryExpression as UnaryExpression,\n  updateExpression as UpdateExpression,\n  variableDeclaration as VariableDeclaration,\n  variableDeclarator as VariableDeclarator,\n  whileStatement as WhileStatement,\n  withStatement as WithStatement,\n  assignmentPattern as AssignmentPattern,\n  arrayPattern as ArrayPattern,\n  arrowFunctionExpression as ArrowFunctionExpression,\n  classBody as ClassBody,\n  classExpression as ClassExpression,\n  classDeclaration as ClassDeclaration,\n  exportAllDeclaration as ExportAllDeclaration,\n  exportDefaultDeclaration as ExportDefaultDeclaration,\n  exportNamedDeclaration as ExportNamedDeclaration,\n  exportSpecifier as ExportSpecifier,\n  forOfStatement as ForOfStatement,\n  importDeclaration as ImportDeclaration,\n  importDefaultSpecifier as ImportDefaultSpecifier,\n  importNamespaceSpecifier as ImportNamespaceSpecifier,\n  importSpecifier as ImportSpecifier,\n  importExpression as ImportExpression,\n  metaProperty as MetaProperty,\n  classMethod as ClassMethod,\n  objectPattern as ObjectPattern,\n  spreadElement as SpreadElement,\n  super as Super,\n  taggedTemplateExpression as TaggedTemplateExpression,\n  templateElement as TemplateElement,\n  templateLiteral as TemplateLiteral,\n  yieldExpression as YieldExpression,\n  awaitExpression as AwaitExpression,\n  import as Import,\n  bigIntLiteral as BigIntLiteral,\n  exportNamespaceSpecifier as ExportNamespaceSpecifier,\n  optionalMemberExpression as OptionalMemberExpression,\n  optionalCallExpression as OptionalCallExpression,\n  classProperty as ClassProperty,\n  classAccessorProperty as ClassAccessorProperty,\n  classPrivateProperty as ClassPrivateProperty,\n  classPrivateMethod as ClassPrivateMethod,\n  privateName as PrivateName,\n  staticBlock as StaticBlock,\n  anyTypeAnnotation as AnyTypeAnnotation,\n  arrayTypeAnnotation as ArrayTypeAnnotation,\n  booleanTypeAnnotation as BooleanTypeAnnotation,\n  booleanLiteralTypeAnnotation as BooleanLiteralTypeAnnotation,\n  nullLiteralTypeAnnotation as NullLiteralTypeAnnotation,\n  classImplements as ClassImplements,\n  declareClass as DeclareClass,\n  declareFunction as DeclareFunction,\n  declareInterface as DeclareInterface,\n  declareModule as DeclareModule,\n  declareModuleExports as DeclareModuleExports,\n  declareTypeAlias as DeclareTypeAlias,\n  declareOpaqueType as DeclareOpaqueType,\n  declareVariable as DeclareVariable,\n  declareExportDeclaration as DeclareExportDeclaration,\n  declareExportAllDeclaration as DeclareExportAllDeclaration,\n  declaredPredicate as DeclaredPredicate,\n  existsTypeAnnotation as ExistsTypeAnnotation,\n  functionTypeAnnotation as FunctionTypeAnnotation,\n  functionTypeParam as FunctionTypeParam,\n  genericTypeAnnotation as GenericTypeAnnotation,\n  inferredPredicate as InferredPredicate,\n  interfaceExtends as InterfaceExtends,\n  interfaceDeclaration as InterfaceDeclaration,\n  interfaceTypeAnnotation as InterfaceTypeAnnotation,\n  intersectionTypeAnnotation as IntersectionTypeAnnotation,\n  mixedTypeAnnotation as MixedTypeAnnotation,\n  emptyTypeAnnotation as EmptyTypeAnnotation,\n  nullableTypeAnnotation as NullableTypeAnnotation,\n  numberLiteralTypeAnnotation as NumberLiteralTypeAnnotation,\n  numberTypeAnnotation as NumberTypeAnnotation,\n  objectTypeAnnotation as ObjectTypeAnnotation,\n  objectTypeInternalSlot as ObjectTypeInternalSlot,\n  objectTypeCallProperty as ObjectTypeCallProperty,\n  objectTypeIndexer as ObjectTypeIndexer,\n  objectTypeProperty as ObjectTypeProperty,\n  objectTypeSpreadProperty as ObjectTypeSpreadProperty,\n  opaqueType as OpaqueType,\n  qualifiedTypeIdentifier as QualifiedTypeIdentifier,\n  stringLiteralTypeAnnotation as StringLiteralTypeAnnotation,\n  stringTypeAnnotation as StringTypeAnnotation,\n  symbolTypeAnnotation as SymbolTypeAnnotation,\n  thisTypeAnnotation as ThisTypeAnnotation,\n  tupleTypeAnnotation as TupleTypeAnnotation,\n  typeofTypeAnnotation as TypeofTypeAnnotation,\n  typeAlias as TypeAlias,\n  typeAnnotation as TypeAnnotation,\n  typeCastExpression as TypeCastExpression,\n  typeParameter as TypeParameter,\n  typeParameterDeclaration as TypeParameterDeclaration,\n  typeParameterInstantiation as TypeParameterInstantiation,\n  unionTypeAnnotation as UnionTypeAnnotation,\n  variance as Variance,\n  voidTypeAnnotation as VoidTypeAnnotation,\n  enumDeclaration as EnumDeclaration,\n  enumBooleanBody as EnumBooleanBody,\n  enumNumberBody as EnumNumberBody,\n  enumStringBody as EnumStringBody,\n  enumSymbolBody as EnumSymbolBody,\n  enumBooleanMember as EnumBooleanMember,\n  enumNumberMember as EnumNumberMember,\n  enumStringMember as EnumStringMember,\n  enumDefaultedMember as EnumDefaultedMember,\n  indexedAccessType as IndexedAccessType,\n  optionalIndexedAccessType as OptionalIndexedAccessType,\n  jsxAttribute as JSXAttribute,\n  jsxClosingElement as JSXClosingElement,\n  jsxElement as JSXElement,\n  jsxEmptyExpression as JSXEmptyExpression,\n  jsxExpressionContainer as JSXExpressionContainer,\n  jsxSpreadChild as JSXSpreadChild,\n  jsxIdentifier as JSXIdentifier,\n  jsxMemberExpression as JSXMemberExpression,\n  jsxNamespacedName as JSXNamespacedName,\n  jsxOpeningElement as JSXOpeningElement,\n  jsxSpreadAttribute as JSXSpreadAttribute,\n  jsxText as JSXText,\n  jsxFragment as JSXFragment,\n  jsxOpeningFragment as JSXOpeningFragment,\n  jsxClosingFragment as JSXClosingFragment,\n  noop as Noop,\n  placeholder as Placeholder,\n  v8IntrinsicIdentifier as V8IntrinsicIdentifier,\n  argumentPlaceholder as ArgumentPlaceholder,\n  bindExpression as BindExpression,\n  importAttribute as ImportAttribute,\n  decorator as Decorator,\n  doExpression as DoExpression,\n  exportDefaultSpecifier as ExportDefaultSpecifier,\n  recordExpression as RecordExpression,\n  tupleExpression as TupleExpression,\n  decimalLiteral as DecimalLiteral,\n  moduleExpression as ModuleExpression,\n  topicReference as TopicReference,\n  pipelineTopicExpression as PipelineTopicExpression,\n  pipelineBareFunction as PipelineBareFunction,\n  pipelinePrimaryTopicReference as PipelinePrimaryTopicReference,\n  tsParameterProperty as TSParameterProperty,\n  tsDeclareFunction as TSDeclareFunction,\n  tsDeclareMethod as TSDeclareMethod,\n  tsQualifiedName as TSQualifiedName,\n  tsCallSignatureDeclaration as TSCallSignatureDeclaration,\n  tsConstructSignatureDeclaration as TSConstructSignatureDeclaration,\n  tsPropertySignature as TSPropertySignature,\n  tsMethodSignature as TSMethodSignature,\n  tsIndexSignature as TSIndexSignature,\n  tsAnyKeyword as TSAnyKeyword,\n  tsBooleanKeyword as TSBooleanKeyword,\n  tsBigIntKeyword as TSBigIntKeyword,\n  tsIntrinsicKeyword as TSIntrinsicKeyword,\n  tsNeverKeyword as TSNeverKeyword,\n  tsNullKeyword as TSNullKeyword,\n  tsNumberKeyword as TSNumberKeyword,\n  tsObjectKeyword as TSObjectKeyword,\n  tsStringKeyword as TSStringKeyword,\n  tsSymbolKeyword as TSSymbolKeyword,\n  tsUndefinedKeyword as TSUndefinedKeyword,\n  tsUnknownKeyword as TSUnknownKeyword,\n  tsVoidKeyword as TSVoidKeyword,\n  tsThisType as TSThisType,\n  tsFunctionType as TSFunctionType,\n  tsConstructorType as TSConstructorType,\n  tsTypeReference as TSTypeReference,\n  tsTypePredicate as TSTypePredicate,\n  tsTypeQuery as TSTypeQuery,\n  tsTypeLiteral as TSTypeLiteral,\n  tsArrayType as TSArrayType,\n  tsTupleType as TSTupleType,\n  tsOptionalType as TSOptionalType,\n  tsRestType as TSRestType,\n  tsNamedTupleMember as TSNamedTupleMember,\n  tsUnionType as TSUnionType,\n  tsIntersectionType as TSIntersectionType,\n  tsConditionalType as TSConditionalType,\n  tsInferType as TSInferType,\n  tsParenthesizedType as TSParenthesizedType,\n  tsTypeOperator as TSTypeOperator,\n  tsIndexedAccessType as TSIndexedAccessType,\n  tsMappedType as TSMappedType,\n  tsLiteralType as TSLiteralType,\n  tsExpressionWithTypeArguments as TSExpressionWithTypeArguments,\n  tsInterfaceDeclaration as TSInterfaceDeclaration,\n  tsInterfaceBody as TSInterfaceBody,\n  tsTypeAliasDeclaration as TSTypeAliasDeclaration,\n  tsInstantiationExpression as TSInstantiationExpression,\n  tsAsExpression as TSAsExpression,\n  tsSatisfiesExpression as TSSatisfiesExpression,\n  tsTypeAssertion as TSTypeAssertion,\n  tsEnumDeclaration as TSEnumDeclaration,\n  tsEnumMember as TSEnumMember,\n  tsModuleDeclaration as TSModuleDeclaration,\n  tsModuleBlock as TSModuleBlock,\n  tsImportType as TSImportType,\n  tsImportEqualsDeclaration as TSImportEqualsDeclaration,\n  tsExternalModuleReference as TSExternalModuleReference,\n  tsNonNullExpression as TSNonNullExpression,\n  tsExportAssignment as TSExportAssignment,\n  tsNamespaceExportDeclaration as TSNamespaceExportDeclaration,\n  tsTypeAnnotation as TSTypeAnnotation,\n  tsTypeParameterInstantiation as TSTypeParameterInstantiation,\n  tsTypeParameterDeclaration as TSTypeParameterDeclaration,\n  tsTypeParameter as TSTypeParameter,\n  numberLiteral as NumberLiteral,\n  regexLiteral as RegexLiteral,\n  restProperty as RestProperty,\n  spreadProperty as SpreadProperty,\n} from \"./index.ts\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,OAAA", "ignoreList": []}