{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_globals", "_t", "t", "_cache", "_visitors", "NOT_LOCAL_BINDING", "assignmentExpression", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isCallExpression", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMemberExpression", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "expressionStatement", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "buildUndefinedNode", "sequenceExpression", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "scope", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "ReferencedIdentifier", "state", "references", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "Object", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "bindings", "CatchClause", "Function", "params", "param", "isFunctionExpression", "ClassExpression", "TSTypeAnnotation", "skip", "uid", "<PERSON><PERSON>", "constructor", "block", "inited", "labels", "globals", "uids", "data", "crawling", "cached", "scopeCache", "set", "Map", "_parent", "_path", "shouldSkip", "<PERSON><PERSON><PERSON>", "parentPath", "isScope", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "hub", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "dump", "sep", "repeat", "console", "log", "violations", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "registerConstantViolation", "ids", "getAssignmentIdentifiers", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "right", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "expressions", "tag", "noGlobals", "quasi", "isStringLiteral", "setData", "val", "getData", "removeData", "init", "crawl", "create", "programParent", "isExplodedVisitor", "visit", "enter", "call", "typeVisitors", "traverse", "ref", "opts", "getPatternParent", "isBlockStatement", "isProgram", "isSwitchStatement", "unique", "isFunction", "pushContainer", "isLoop", "isCatchClause", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "Error", "isFunctionParent", "isBlockParent", "getAllBindings", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "noUids", "includes", "contextVariables", "parentHasBinding", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "hoistVariables", "emit", "seen", "Set", "isVariableDeclarator", "has", "add", "firstId", "_firstId", "isFor", "replaceWith", "remove", "expr", "isForStatement", "exports", "default", "builtin", "prototype", "_renameFromMap", "map", "_generateUid", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "getAllBindingsOfKind", "kinds", "defineProperties", "parentBlock", "configurable", "enumerable"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globals from \"globals\";\nimport {\n  NOT_LOCAL_BINDING,\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isCallExpression,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMemberExpression,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  expressionStatement,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n  buildUndefinedNode,\n  sequenceExpression,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport { isExplodedVisitor } from \"../visitors.ts\";\n\ntype NodePart = string | number | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\n//\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.node.id &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.node.id[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.node.id &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.node.id[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n  TSTypeAnnotation(path) {\n    path.skip();\n  },\n};\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport { Scope as default };\nclass Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  inited;\n\n  labels: Map<string, NodePath<t.LabeledStatement>>;\n  bindings: { [name: string]: Binding };\n  references: { [name: string]: true };\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  uids: { [name: string]: boolean };\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = Object.keys(globals.builtin);\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path?.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name).replace(/^_+/, \"\").replace(/\\d+$/g, \"\");\n\n    let uid;\n    let i = 1;\n    do {\n      uid = `_${name}`;\n      if (i > 1) uid += i;\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    program.references[uid] = true;\n    program.uids[uid] = true;\n\n    return uid;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.path.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return buildUndefinedNode();\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getAssignmentIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      parent.references[name] = true;\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          local.reassign(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.uids[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    return !!this.getProgramParent().references[name];\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", { noGlobals: true }) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isMemberExpression(node)) {\n      return (\n        !node.computed &&\n        isIdentifier(node.object) &&\n        node.object.name === \"Symbol\" &&\n        isIdentifier(node.property) &&\n        node.property.name !== \"for\" &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true })\n      );\n    } else if (isCallExpression(node)) {\n      return (\n        matchesPattern(node.callee, \"Symbol.for\") &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true }) &&\n        node.arguments.length === 1 &&\n        t.isStringLiteral(node.arguments[0])\n      );\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    this.references = Object.create(null);\n    this.bindings = Object.create(null);\n    this.globals = Object.create(null);\n    this.uids = Object.create(null);\n    this.data = Object.create(null);\n\n    const programParent = this.getProgramParent();\n    if (programParent.crawling) return;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\" && isExplodedVisitor(collectorVisitor)) {\n      for (const visit of collectorVisitor.enter) {\n        visit.call(state, path, state);\n      }\n      const typeVisitors = collectorVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    path.traverse(collectorVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getAssignmentIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.ArrayPattern | t.Identifier | t.ObjectPattern;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      path.isFunction() &&\n      // @ts-expect-error ArrowFunctionExpression never has a name\n      !path.node.name &&\n      isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      path.ensureBlock();\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?: boolean | { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    if (!name) return false;\n    let scope: Scope = this;\n    do {\n      if (scope.hasOwnBinding(name)) {\n        return true;\n      }\n    } while ((scope = scope.parent));\n\n    // TODO: Only accept the object form.\n    let noGlobals;\n    let noUids;\n    if (typeof opts === \"object\") {\n      noGlobals = opts.noGlobals;\n      noUids = opts.noUids;\n    } else if (typeof opts === \"boolean\") {\n      noGlobals = opts;\n    }\n\n    if (!noUids && this.hasUid(name)) return true;\n    if (!noGlobals && Scope.globals.includes(name)) return true;\n    if (!noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    let scope: Scope = this;\n    do {\n      if (scope.uids[name]) {\n        scope.uids[name] = false;\n      }\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Hoist all the `var` variable to the beginning of the function/program\n   * scope where their binding will be actually defined. For exmaple,\n   *     { var x = 2 }\n   * will be transformed to\n   *     var x; { x = 2 }\n   *\n   * @param emit A custom function to emit `var` declarations, for example to\n   *   emit them in a different scope.\n   */\n  hoistVariables(\n    emit: (id: t.Identifier, hasInit: boolean) => void = id =>\n      this.push({ id }),\n  ) {\n    this.crawl();\n\n    const seen = new Set();\n    for (const name of Object.keys(this.bindings)) {\n      const binding = this.bindings[name];\n      if (!binding) continue;\n      const { path } = binding;\n      if (!path.isVariableDeclarator()) continue;\n      const { parent, parentPath } = path;\n\n      if (parent.kind !== \"var\" || seen.has(parent)) continue;\n      seen.add(path.parent);\n\n      let firstId;\n      const init = [];\n      for (const decl of parent.declarations) {\n        firstId ??= decl.id;\n        if (decl.init) {\n          init.push(assignmentExpression(\"=\", decl.id, decl.init));\n        }\n\n        const ids = Object.keys(getBindingIdentifiers(decl, false, true, true));\n        for (const name of ids) {\n          emit(identifier(name), decl.init != null);\n        }\n      }\n\n      // for (var i in test)\n      if (parentPath.parentPath.isFor({ left: parent })) {\n        parentPath.replaceWith(firstId);\n      } else if (init.length === 0) {\n        parentPath.remove();\n      } else {\n        const expr = init.length === 1 ? init[0] : sequenceExpression(init);\n        if (parentPath.parentPath.isForStatement({ init: parent })) {\n          parentPath.replaceWith(expr);\n        } else {\n          parentPath.replaceWith(expressionStatement(expr));\n        }\n      }\n    }\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /** @deprecated Not used in our codebase */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._renameFromMap = function _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  };\n\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.traverse = function <S>(\n    this: Scope,\n    node: any,\n    opts: any,\n    state?: S,\n  ) {\n    traverse(node, opts, this, state, this.path);\n  };\n\n  /**\n   * Generate an `_id1`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._generateUid = function _generateUid(\n    name: string,\n    i: number,\n  ) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  };\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.toArray = function toArray(\n    this: Scope,\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.path.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.path.hub.addHelper(helperName), args);\n  };\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.getAllBindingsOfKind = function getAllBindingsOfKind(\n    ...kinds: string[]\n  ): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  };\n\n  Object.defineProperties(Scope.prototype, {\n    parentBlock: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.parent;\n      },\n    },\n    hub: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.hub;\n      },\n    },\n  });\n}\n\ntype _Binding = Binding;\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Scope {\n  export type Binding = _Binding;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAkDsB,IAAAK,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAN,OAAA;AAEA,IAAAO,SAAA,GAAAP,OAAA;AAAmD;EArDjDQ,iBAAiB;EACjBC,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,gBAAgB;EAChBC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,kBAAkB;EAClBC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,mBAAmB;EACnBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,mBAAmB;EACnBC,kBAAkB;EAClBC;AAAkB,IAAApD,EAAA;AASpB,SAASqD,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAInC,mBAAmB,CAACiC,IAAI,CAAC,IAAIJ,mBAAmB,CAACI,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACzC,sBAAsB,CAACsC,IAAI,CAAC,IAC3BpC,wBAAwB,CAACoC,IAAI,CAAC,IAC9BjC,mBAAmB,CAACiC,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAACrC,wBAAwB,CAACoC,IAAI,CAAC,IAAIjC,mBAAmB,CAACiC,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACtC,0BAA0B,CAACqC,IAAI,CAAC,IAC/BpC,wBAAwB,CAACoC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI9B,iBAAiB,CAAC6B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACLjC,SAAS,CAACgC,IAAI,CAAC,IACf,CAAC5B,aAAa,CAAC4B,IAAI,CAAC,IACpB,CAACxB,eAAe,CAACwB,IAAI,CAAC,IACtB,CAACrB,iBAAiB,CAACqB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;MACXT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AASA,MAAMyB,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEP,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDQ,WAAWA,CAACT,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACU,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIV,IAAI,CAAC7D,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAI6D,IAAI,CAAChC,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAM2C,MAAM,GACVX,IAAI,CAACI,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIN,IAAI,CAACI,KAAK,CAACG,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDa,iBAAiBA,CAACb,IAAI,EAAE;IAEtB,MAAMW,MAAM,GAAGX,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACC,UAAU,CAACnC,IAAI,CAACkB,IAAI,CAAC;EAC7B,CAAC;EAEDkB,aAAaA,CAAClB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMzB,IAAI,GAAGS,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAIX,IAAI,CAAC4B,SAAS,CAAC,CAAC,IAAI5B,IAAI,CAACrD,YAAY,CAAC,CAAC,EAAE;MAC3C8E,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIT,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEjB,IAAI,CAAC;IAC1C;EACF,CAAC;EAED8B,iBAAiB,EAAE;IACjBC,IAAIA,CAACtB,IAAI,EAAE;MACT,MAAM;QAAE5B,IAAI;QAAEgC;MAAM,CAAC,GAAGJ,IAAI;MAE5B,IAAIlE,sBAAsB,CAACsC,IAAI,CAAC,EAAE;MAClC,MAAM6B,MAAM,GAAG7B,IAAI,CAACQ,WAAW;MAC/B,IAAI/C,kBAAkB,CAACoE,MAAM,CAAC,IAAIhE,qBAAqB,CAACgE,MAAM,CAAC,EAAE;QAC/D,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM+B,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAAChC,EAAE,CAACN,IAAI,CAAC;QACzCqC,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAI9C,qBAAqB,CAAC+C,MAAM,CAAC,EAAE;QACxC,KAAK,MAAMyB,IAAI,IAAIzB,MAAM,CAAC0B,YAAY,EAAE;UACtC,KAAK,MAAMzC,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACvG,qBAAqB,CAACoG,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC;YACtCqC,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAED8B,gBAAgBA,CAAC9B,IAAI,EAAE;IACrBA,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAACZ,IAAI,CAAC;EACvD,CAAC;EAED+B,oBAAoBA,CAAC/B,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACgB,WAAW,CAAClD,IAAI,CAACkB,IAAI,CAAC;EAC9B,CAAC;EAEDiC,gBAAgBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;EACrC,CAAC;EAEDkC,eAAeA,CAAClC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAAC5B,IAAI,CAAC+D,QAAQ,KAAK,QAAQ,EAAE;MACnCnB,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC;EACF,CAAC;EAEDoC,WAAWA,CAACpC,IAAI,EAAE;IAChB,IAAII,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACtB,IAAIA,KAAK,CAACJ,IAAI,KAAKA,IAAI,EAAEI,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE7C,MAAMA,MAAM,GAAGP,KAAK,CAACU,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAACnE,kBAAkB,CAAC,CAAC,IAAImE,IAAI,CAAC5B,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGQ,IAAI,CAAC5B,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpBc,IAAI,CAACI,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGc,IAAI,CAACI,KAAK,CAACO,MAAM,CAACa,UAAU,CAACtC,IAAI,CAAC;IAChE;EACF,CAAC;EAEDoD,WAAWA,CAACtC,IAAI,EAAE;IAChBA,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;EACzC,CAAC;EAEDuC,QAAQA,CAACvC,IAAI,EAAE;IACb,MAAMwC,MAAuB,GAAGxC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMuC,KAAK,IAAID,MAAM,EAAE;MAC1BxC,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAEiC,KAAK,CAAC;IAC5C;IAKA,IACEzC,IAAI,CAAC0C,oBAAoB,CAAC,CAAC,IAC3B1C,IAAI,CAAC5B,IAAI,CAACoB,EAAE,IAEZ,CAACQ,IAAI,CAAC5B,IAAI,CAACoB,EAAE,CAACtE,iBAAiB,CAAC,EAChC;MACA8E,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED2C,eAAeA,CAAC3C,IAAI,EAAE;IACpB,IACEA,IAAI,CAAC5B,IAAI,CAACoB,EAAE,IAEZ,CAACQ,IAAI,CAAC5B,IAAI,CAACoB,EAAE,CAACtE,iBAAiB,CAAC,EAChC;MACA8E,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EACD4C,gBAAgBA,CAAC5C,IAAI,EAAE;IACrBA,IAAI,CAAC6C,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAED,IAAIC,GAAG,GAAG,CAAC;AAKX,MAAMC,KAAK,CAAC;EAoBVC,WAAWA,CAAChD,IAAsC,EAAE;IAAA,KAnBpD8C,GAAG;IAAA,KAEH9C,IAAI;IAAA,KACJiD,KAAK;IAAA,KAELC,MAAM;IAAA,KAENC,MAAM;IAAA,KACNd,QAAQ;IAAA,KACRpB,UAAU;IAAA,KACVmC,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAEnF;IAAK,CAAC,GAAG4B,IAAI;IACrB,MAAMwD,MAAM,GAAGC,YAAU,CAACvD,GAAG,CAAC9B,IAAI,CAAC;IAGnC,IAAI,CAAAoF,MAAM,oBAANA,MAAM,CAAExD,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOwD,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACtF,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAAC0E,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG7E,IAAI;IACjB,IAAI,CAAC4B,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACmD,MAAM,GAAG,IAAIQ,GAAG,CAAC,CAAC;IACvB,IAAI,CAACT,MAAM,GAAG,KAAK;EACrB;EAcA,IAAIvC,MAAMA,CAAA,EAAG;IAAA,IAAAiD,OAAA;IACX,IAAIjD,MAAM;MACRX,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAAA,IAAA6D,KAAA;MAED,MAAMC,UAAU,GAAG9D,IAAI,CAACV,GAAG,KAAK,KAAK,IAAIU,IAAI,CAAC+D,OAAO,KAAK,YAAY;MACtE/D,IAAI,GAAGA,IAAI,CAACgE,UAAU;MACtB,IAAIF,UAAU,IAAI9D,IAAI,CAAC1D,QAAQ,CAAC,CAAC,EAAE0D,IAAI,GAAGA,IAAI,CAACgE,UAAU;MACzD,KAAAH,KAAA,GAAI7D,IAAI,aAAJ6D,KAAA,CAAMI,OAAO,CAAC,CAAC,EAAEtD,MAAM,GAAGX,IAAI;IACpC,CAAC,QAAQA,IAAI,IAAI,CAACW,MAAM;IAExB,QAAAiD,OAAA,GAAOjD,MAAM,qBAANiD,OAAA,CAAQxD,KAAK;EACtB;EAMA8D,6BAA6BA,CAAChF,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAAC2E,qBAAqB,CAACjF,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;EACtB;EAMA2E,qBAAqBA,CAACjF,IAAa,EAAE;IACnC,OAAO3D,UAAU,CAAC,IAAI,CAAC6I,WAAW,CAAClF,IAAI,CAAC,CAAC;EAC3C;EAMAkF,WAAWA,CAAClF,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG3B,YAAY,CAAC2B,IAAI,CAAC,CAACmF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAEjE,IAAIvB,GAAG;IACP,IAAIwB,CAAC,GAAG,CAAC;IACT,GAAG;MACDxB,GAAG,GAAG,IAAI5D,IAAI,EAAE;MAChB,IAAIoF,CAAC,GAAG,CAAC,EAAExB,GAAG,IAAIwB,CAAC;MACnBA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACC,QAAQ,CAACzB,GAAG,CAAC,IAClB,IAAI,CAAC0B,UAAU,CAAC1B,GAAG,CAAC,IACpB,IAAI,CAAC2B,SAAS,CAAC3B,GAAG,CAAC,IACnB,IAAI,CAAC4B,YAAY,CAAC5B,GAAG,CAAC;IAGxB,MAAM6B,OAAO,GAAG,IAAI,CAACpE,gBAAgB,CAAC,CAAC;IACvCoE,OAAO,CAAC1D,UAAU,CAAC6B,GAAG,CAAC,GAAG,IAAI;IAC9B6B,OAAO,CAACtB,IAAI,CAACP,GAAG,CAAC,GAAG,IAAI;IAExB,OAAOA,GAAG;EACZ;EAEA8B,sBAAsBA,CAACxG,IAAY,EAAEyG,WAAoB,EAAE;IACzD,MAAMxG,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAACyG,IAAI,CAAC,GAAG,CAAC;IACxBtF,EAAE,GAAGA,EAAE,CAAC6E,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIQ,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACT,WAAW,CAAC5E,EAAE,CAACuF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAC5G,IAAY,EAAEyG,WAAoB,EAAE;IACnE,OAAOtJ,UAAU,CAAC,IAAI,CAACqJ,sBAAsB,CAACxG,IAAI,EAAEyG,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAAC7G,IAAY,EAAW;IAC9B,IAAIpB,gBAAgB,CAACoB,IAAI,CAAC,IAAIvB,OAAO,CAACuB,IAAI,CAAC,IAAIP,gBAAgB,CAACO,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,EAAE;QACX,OAAOA,OAAO,CAAC2D,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAACpG,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAiG,qBAAqBA,CAAC/G,IAAY,EAAEgH,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAAC7G,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAACwF,gCAAgC,CAAC5G,IAAI,CAAC;MACtD,IAAI,CAACgH,QAAQ,EAAE;QACb,IAAI,CAACtG,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEA6F,0BAA0BA,CACxBxG,KAAc,EACdyG,IAAiB,EACjBpG,IAAY,EACZM,EAAO,EACP;IAEA,IAAI8F,IAAI,KAAK,OAAO,EAAE;IAItB,IAAIzG,KAAK,CAACyG,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACdzG,KAAK,CAACyG,IAAI,KAAK,KAAK,IACpBzG,KAAK,CAACyG,IAAI,KAAK,OAAO,IACtBzG,KAAK,CAACyG,IAAI,KAAK,QAAQ,IAEtBzG,KAAK,CAACyG,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACvF,IAAI,CAACwF,GAAG,CAACC,UAAU,CAC5BjG,EAAE,EACF,0BAA0BN,IAAI,GAAG,EACjCwG,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMtE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACoE,OAAO,CAAC;IACxC,IAAIrE,OAAO,EAAE;MACXsE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC1G,IAAI;MACpD,MAAM4G,OAAO,GAAG,IAAIC,gBAAO,CAACxE,OAAO,EAAEqE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAI9F,KAAY,GAAG,IAAI;IACvB,GAAG;MACDgG,OAAO,CAACC,GAAG,CAAC,GAAG,EAAEjG,KAAK,CAAC6C,KAAK,CAAC3E,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;QACpCkH,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEnH,IAAI,EAAE;UACtBgG,QAAQ,EAAE3D,OAAO,CAAC2D,QAAQ;UAC1BjE,UAAU,EAAEM,OAAO,CAACN,UAAU;UAC9BqF,UAAU,EAAE/E,OAAO,CAACH,kBAAkB,CAAC1C,MAAM;UAC7C4G,IAAI,EAAE/D,OAAO,CAAC+D;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASlF,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9ByF,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAEA3B,QAAQA,CAACrF,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACqH,QAAQ,CAACrH,IAAI,CAAC;EAC9B;EAEAqH,QAAQA,CAACrH,IAAY,EAAE;IACrB,OAAO,IAAI,CAACiE,MAAM,CAACjD,GAAG,CAAChB,IAAI,CAAC;EAC9B;EAEAsH,aAAaA,CAACxG,IAAkC,EAAE;IAChD,IAAI,CAACmD,MAAM,CAACO,GAAG,CAAC1D,IAAI,CAAC5B,IAAI,CAACqI,KAAK,CAACvH,IAAI,EAAEc,IAAI,CAAC;EAC7C;EAEAY,mBAAmBA,CAACZ,IAAc,EAAE;IAClC,IAAIA,IAAI,CAAC0G,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACxG,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC/D,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACuE,eAAe,CAAC,SAAS,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAAC9C,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAMyE,YAAY,GAAG3B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAEoF;MAAK,CAAC,GAAGtF,IAAI,CAAC5B,IAAI;MAC1B,KAAK,MAAM6B,MAAM,IAAI0B,YAAY,EAAE;QACjC,IAAI,CAACnB,eAAe,CAClB8E,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3DrF,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAID,IAAI,CAACnE,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAImE,IAAI,CAAC5B,IAAI,CAACuI,OAAO,EAAE;MACvB,IAAI,CAACnG,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAAC7D,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAMyK,iBAAiB,GACrB5G,IAAI,CAAC5B,IAAI,CAACyI,UAAU,KAAK,MAAM,IAAI7G,IAAI,CAAC5B,IAAI,CAACyI,UAAU,KAAK,QAAQ;MACtE,MAAMpI,UAAU,GAAGuB,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAM4G,SAAS,IAAIrI,UAAU,EAAE;QAClC,MAAMsI,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAAC1I,IAAI,CAACyI,UAAU,KAAK,MAAM,IACnCC,SAAS,CAAC1I,IAAI,CAACyI,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAACrG,eAAe,CAACuG,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAI9G,IAAI,CAAChC,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAMiC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAACpE,kBAAkB,CAAC,CAAC,IAC3BoE,MAAM,CAAChE,qBAAqB,CAAC,CAAC,IAC9BgE,MAAM,CAAC/C,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAAC0D,mBAAmB,CAACX,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACO,eAAe,CAAC,SAAS,EAAER,IAAI,CAAC;IACvC;EACF;EAEA/B,kBAAkBA,CAAA,EAAG;IACnB,OAAOA,kBAAkB,CAAC,CAAC;EAC7B;EAEAgJ,yBAAyBA,CAACjH,IAAc,EAAE;IACxC,MAAMkH,GAAG,GAAGlH,IAAI,CAACmH,wBAAwB,CAAC,CAAC;IAC3C,KAAK,MAAMjI,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACqF,GAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAAC5F,UAAU,CAACtC,IAAI,CAAC,aAArBkI,gBAAA,CAAuBC,QAAQ,CAACrH,IAAI,CAAC;IACvC;EACF;EAEAQ,eAAeA,CACb8E,IAAqB,EACrBtF,IAAc,EACdsH,WAAqB,GAAGtH,IAAI,EAC5B;IACA,IAAI,CAACsF,IAAI,EAAE,MAAM,IAAIiC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAIvH,IAAI,CAAC9C,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAMsK,WAA4B,GAAGxH,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAIuH,WAAW,EAAE;QAChC,IAAI,CAAChH,eAAe,CAAC8E,IAAI,EAAErF,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMU,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAM2G,GAAG,GAAGlH,IAAI,CAACyH,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMvI,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACqF,GAAG,CAAC,EAAE;MACnCvG,MAAM,CAACM,UAAU,CAAC/B,IAAI,CAAC,GAAG,IAAI;MAE9B,KAAK,MAAMM,EAAE,IAAI0H,GAAG,CAAChI,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAAC6I,aAAa,CAACxI,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACtD,UAAU,KAAKiE,EAAE,EAAE;UAE7B,IAAI,CAAC6F,0BAA0B,CAACxG,KAAK,EAAEyG,IAAI,EAAEpG,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACTA,KAAK,CAACwI,QAAQ,CAACC,WAAW,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACjF,QAAQ,CAACnD,IAAI,CAAC,GAAG,IAAIyI,gBAAO,CAAC;YAChCpM,UAAU,EAAEiE,EAAE;YACdY,KAAK,EAAE,IAAI;YACXJ,IAAI,EAAEsH,WAAW;YACjBhC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEAsC,SAASA,CAACxJ,IAAoC,EAAE;IAC9C,IAAI,CAACgF,OAAO,CAAChF,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEAyJ,MAAMA,CAAC3I,IAAY,EAAW;IAC5B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACiD,IAAI,CAACnE,IAAI,CAAC,EAAE,OAAO,IAAI;IACnC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEA8D,SAASA,CAACvF,IAAY,EAAW;IAC/B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACgD,OAAO,CAAClE,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEA+D,YAAYA,CAACxF,IAAY,EAAW;IAClC,OAAO,CAAC,CAAC,IAAI,CAACqB,gBAAgB,CAAC,CAAC,CAACU,UAAU,CAAC/B,IAAI,CAAC;EACnD;EAEA4I,MAAMA,CAAC1J,IAAY,EAAE2J,aAAuB,EAAW;IACrD,IAAI7L,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACqC,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAIwG,aAAa,EAAE,OAAOxG,OAAO,CAAC2D,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLlI,gBAAgB,CAACoB,IAAI,CAAC,IACtBN,cAAc,CAACM,IAAI,CAAC,IACpBP,gBAAgB,CAACO,IAAI,CAAC,IACtBL,aAAa,CAACK,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzC,OAAO,CAACyC,IAAI,CAAC,EAAE;MAAA,IAAA4J,gBAAA;MACxB,IAAI5J,IAAI,CAAC6J,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAAC1J,IAAI,CAAC6J,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAA5J,IAAI,CAAC8J,UAAU,qBAAfF,gBAAA,CAAiBtJ,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAACoJ,MAAM,CAAC1J,IAAI,CAAC+J,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAInM,WAAW,CAACwC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAMgK,MAAM,IAAIhK,IAAI,CAAC+J,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAItM,QAAQ,CAAC2C,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAAC0J,MAAM,CAAC1J,IAAI,CAACmB,IAAI,EAAEwI,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAAC1J,IAAI,CAACiK,KAAK,EAAEN,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIvM,iBAAiB,CAAC4C,IAAI,CAAC,IAAIT,iBAAiB,CAACS,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAMkK,IAAI,IAAIlK,IAAI,CAACmK,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAEP,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAItL,kBAAkB,CAAC2B,IAAI,CAAC,IAAIV,kBAAkB,CAACU,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAMoK,IAAI,IAAIpK,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC0I,MAAM,CAACU,IAAI,EAAET,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzL,QAAQ,CAAC8B,IAAI,CAAC,EAAE;MAAA,IAAAqK,iBAAA;MACzB,IAAIrK,IAAI,CAACsK,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAAC1J,IAAI,CAACkB,GAAG,EAAEyI,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAU,iBAAA,GAAArK,IAAI,CAAC8J,UAAU,qBAAfO,iBAAA,CAAiB/J,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhC,UAAU,CAAC0B,IAAI,CAAC,EAAE;MAAA,IAAAuK,iBAAA;MAE3B,IAAIvK,IAAI,CAACsK,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAAC1J,IAAI,CAACkB,GAAG,EAAEyI,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAY,iBAAA,GAAAvK,IAAI,CAAC8J,UAAU,qBAAfS,iBAAA,CAAiBjK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAId,gBAAgB,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAACwK,MAAM,EAAE;QACzC,IAAIxK,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC+I,MAAM,CAAC1J,IAAI,CAACW,KAAK,EAAEgJ,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI9K,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAAC0J,MAAM,CAAC1J,IAAI,CAACiB,QAAQ,EAAE0I,aAAa,CAAC;IAClD,CAAC,MAAM,IAAIhL,iBAAiB,CAACqB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAACyK,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACf,MAAM,CAACrI,UAAU,EAAEsI,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIjL,0BAA0B,CAACsB,IAAI,CAAC,EAAE;MAC3C,OACEhB,cAAc,CAACgB,IAAI,CAAC0K,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAACtE,UAAU,CAAC,QAAQ,EAAE;QAAEuE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C,IAAI,CAACjB,MAAM,CAAC1J,IAAI,CAAC4K,KAAK,EAAEjB,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI1L,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MACnC,OACE,CAACA,IAAI,CAACsK,QAAQ,IACdxM,YAAY,CAACkC,IAAI,CAACY,MAAM,CAAC,IACzBZ,IAAI,CAACY,MAAM,CAACE,IAAI,KAAK,QAAQ,IAC7BhD,YAAY,CAACkC,IAAI,CAACa,QAAQ,CAAC,IAC3Bb,IAAI,CAACa,QAAQ,CAACC,IAAI,KAAK,KAAK,IAC5B,CAAC,IAAI,CAACsF,UAAU,CAAC,QAAQ,EAAE;QAAEuE,SAAS,EAAE;MAAK,CAAC,CAAC;IAEnD,CAAC,MAAM,IAAIrN,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MACjC,OACEhB,cAAc,CAACgB,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC,IACzC,CAAC,IAAI,CAACqF,UAAU,CAAC,QAAQ,EAAE;QAAEuE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C3K,IAAI,CAAC4H,SAAS,CAACtH,MAAM,KAAK,CAAC,IAC3B3D,CAAC,CAACkO,eAAe,CAAC7K,IAAI,CAAC4H,SAAS,CAAC,CAAC,CAAC,CAAC;IAExC,CAAC,MAAM;MACL,OAAOrJ,SAAS,CAACyB,IAAI,CAAC;IACxB;EACF;EAMA8K,OAAOA,CAAC5J,GAAoB,EAAE6J,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAAC7F,IAAI,CAAChE,GAAG,CAAC,GAAG6J,GAAG;EAC9B;EAMAC,OAAOA,CAAC9J,GAAoB,EAAO;IACjC,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMkD,IAAI,GAAGlD,KAAK,CAACkD,IAAI,CAAChE,GAAG,CAAC;MAC5B,IAAIgE,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASlD,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAOA0I,UAAUA,CAAC/J,GAAW,EAAE;IACtB,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMkD,IAAI,GAAGlD,KAAK,CAACkD,IAAI,CAAChE,GAAG,CAAC;MAC5B,IAAIgE,IAAI,IAAI,IAAI,EAAElD,KAAK,CAACkD,IAAI,CAAChE,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASc,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEA2I,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACpG,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAACqG,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAMvJ,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,IAAI,CAACiB,UAAU,GAAGW,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAACnH,QAAQ,GAAGT,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI,CAACpG,OAAO,GAAGxB,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAACnG,IAAI,GAAGzB,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAClG,IAAI,GAAG1B,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IAE/B,MAAMC,aAAa,GAAG,IAAI,CAAClJ,gBAAgB,CAAC,CAAC;IAC7C,IAAIkJ,aAAa,CAAClG,QAAQ,EAAE;IAE5B,MAAMvC,KAA0B,GAAG;MACjCC,UAAU,EAAE,EAAE;MACdG,kBAAkB,EAAE,EAAE;MACtBY,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACuB,QAAQ,GAAG,IAAI;IAGpB,IAAIvD,IAAI,CAAC1B,IAAI,KAAK,SAAS,IAAI,IAAAoL,2BAAiB,EAAC5J,gBAAgB,CAAC,EAAE;MAClE,KAAK,MAAM6J,KAAK,IAAI7J,gBAAgB,CAAC8J,KAAK,EAAE;QAC1CD,KAAK,CAACE,IAAI,CAAC7I,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;MAChC;MACA,MAAM8I,YAAY,GAAGhK,gBAAgB,CAACE,IAAI,CAAC1B,IAAI,CAAC;MAChD,IAAIwL,YAAY,EAAE;QAChB,KAAK,MAAMH,KAAK,IAAIG,YAAY,CAACF,KAAK,EAAE;UACtCD,KAAK,CAACE,IAAI,CAAC7I,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;QAChC;MACF;IACF;IACAhB,IAAI,CAAC+J,QAAQ,CAACjK,gBAAgB,EAAEkB,KAAK,CAAC;IACtC,IAAI,CAACuC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMvD,IAAI,IAAIgB,KAAK,CAACgB,WAAW,EAAE;MAEpC,MAAMkF,GAAG,GAAGlH,IAAI,CAACmH,wBAAwB,CAAC,CAAC;MAC3C,KAAK,MAAMjI,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACqF,GAAG,CAAC,EAAE;QACnC,IAAIlH,IAAI,CAACI,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC,EAAE;QACjCuK,aAAa,CAAC7B,SAAS,CAACV,GAAG,CAAChI,IAAI,CAAC,CAAC;MACpC;MAGAc,IAAI,CAACI,KAAK,CAAC6G,yBAAyB,CAACjH,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAMgK,GAAG,IAAIhJ,KAAK,CAACC,UAAU,EAAE;MAClC,MAAMM,OAAO,GAAGyI,GAAG,CAAC5J,KAAK,CAACoB,UAAU,CAACwI,GAAG,CAAC5L,IAAI,CAACc,IAAI,CAAC;MACnD,IAAIqC,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAACuI,GAAG,CAAC;MACxB,CAAC,MAAM;QACLP,aAAa,CAAC7B,SAAS,CAACoC,GAAG,CAAC5L,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAM4B,IAAI,IAAIgB,KAAK,CAACI,kBAAkB,EAAE;MAC3CpB,IAAI,CAACI,KAAK,CAAC6G,yBAAyB,CAACjH,IAAI,CAAC;IAC5C;EACF;EAEAlB,IAAIA,CAACmL,IAMJ,EAAE;IACD,IAAIjK,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;MACpBnB,IAAI,GAAG,IAAI,CAACkK,gBAAgB,CAAC,CAAC,CAAClK,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAACmK,gBAAgB,CAAC,CAAC,IAAI,CAACnK,IAAI,CAACoK,SAAS,CAAC,CAAC,EAAE;MACxDpK,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC,CAACd,IAAI;IACnC;IAEA,IAAIA,IAAI,CAACqK,iBAAiB,CAAC,CAAC,EAAE;MAC5BrK,IAAI,GAAG,CAAC,IAAI,CAACM,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEP,IAAI;IACnE;IAEA,MAAM;MAAEsJ,IAAI;MAAEgB,MAAM;MAAEhF,IAAI,GAAG,KAAK;MAAE9F;IAAG,CAAC,GAAGyK,IAAI;IAM/C,IACE,CAACX,IAAI,IACL,CAACgB,MAAM,KACNhF,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClCtF,IAAI,CAACuK,UAAU,CAAC,CAAC,IAEjB,CAACvK,IAAI,CAAC5B,IAAI,CAACc,IAAI,IACfxD,gBAAgB,CAACsE,IAAI,CAACW,MAAM,EAAE;MAAExB,MAAM,EAAEa,IAAI,CAAC5B;IAAK,CAAC,CAAC,IACpD4B,IAAI,CAACW,MAAM,CAACqF,SAAS,CAACtH,MAAM,IAAIsB,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,IACvDxC,YAAY,CAACsD,EAAE,CAAC,EAChB;MACAQ,IAAI,CAACwK,aAAa,CAAC,QAAQ,EAAEhL,EAAE,CAAC;MAChCQ,IAAI,CAACI,KAAK,CAACI,eAAe,CACxB,OAAO,EACPR,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAIsB,IAAI,CAACyK,MAAM,CAAC,CAAC,IAAIzK,IAAI,CAAC0K,aAAa,CAAC,CAAC,IAAI1K,IAAI,CAACuK,UAAU,CAAC,CAAC,EAAE;MAC9DvK,IAAI,CAAC2K,WAAW,CAAC,CAAC;MAClB3K,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAM0K,UAAU,GAAGX,IAAI,CAACY,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGZ,IAAI,CAACY,WAAW;IAElE,MAAMC,OAAO,GAAG,eAAexF,IAAI,IAAIsF,UAAU,EAAE;IACnD,IAAIG,UAAU,GAAG,CAACT,MAAM,IAAItK,IAAI,CAACoJ,OAAO,CAAC0B,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAM9K,MAAM,GAAGzC,mBAAmB,CAAC8H,IAAI,EAAE,EAAE,CAAC;MAE5CrF,MAAM,CAAC4K,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAI/K,IAAI,CAAgCgL,gBAAgB,CAClE,MAAM,EACN,CAAC/K,MAAM,CACT,CAAC;MACD,IAAI,CAACqK,MAAM,EAAEtK,IAAI,CAACkJ,OAAO,CAAC4B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAGxN,kBAAkB,CAAC+B,EAAE,EAAE8J,IAAI,CAAC;IAC/C,MAAM4B,GAAG,GAAGH,UAAU,CAAC3M,IAAI,CAACuD,YAAY,CAAC7C,IAAI,CAACmM,UAAU,CAAC;IACzDjL,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC8E,IAAI,EAAEyF,UAAU,CAAC7K,GAAG,CAAC,cAAc,CAAC,CAACgL,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMA3K,gBAAgBA,CAAA,EAAG;IACjB,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACoK,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAOhK,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAIwK,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMA7K,iBAAiBA,CAAA,EAAiB;IAChC,IAAIF,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACoL,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAOhL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIV,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACqL,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAOjL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAIwK,KAAK,CACb,8EACF,CAAC;EACH;EAOAjB,gBAAgBA,CAAA,EAAG;IACjB,IAAI9J,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACJ,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOf,KAAK,CAACU,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAASV,KAAK,GAAGA,KAAK,CAACO,MAAM,CAACA,MAAM;IACrC,MAAM,IAAIwK,KAAK,CACb,8EACF,CAAC;EACH;EAMAG,cAAcA,CAAA,EAA4B;IACxC,MAAMpE,GAAG,GAAGtF,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIpJ,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMd,GAAG,IAAIsC,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC7C,IAAI/C,GAAG,IAAI4H,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAAC5H,GAAG,CAAC,GAAGc,KAAK,CAACiC,QAAQ,CAAC/C,GAAG,CAAC;QAChC;MACF;MACAc,KAAK,GAAGA,KAAK,CAACO,MAAM;IACtB,CAAC,QAAQP,KAAK;IAEd,OAAO8G,GAAG;EACZ;EAEAqE,uBAAuBA,CAACrM,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAACoN,oBAAoB,CAACtM,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAoD,UAAUA,CAACtC,IAAY,EAAuB;IAC5C,IAAIkB,KAAY,GAAG,IAAI;IACvB,IAAIqL,YAAY;IAEhB,GAAG;MACD,MAAMlK,OAAO,GAAGnB,KAAK,CAACsH,aAAa,CAACxI,IAAI,CAAC;MACzC,IAAIqC,OAAO,EAAE;QAAA,IAAAmK,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAcvK,SAAS,CAAC,CAAC,IACzBI,OAAO,CAAC+D,IAAI,KAAK,OAAO,IACxB/D,OAAO,CAAC+D,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAO/D,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRrC,IAAI,KAAK,WAAW,IACpBkB,KAAK,CAACJ,IAAI,CAACuK,UAAU,CAAC,CAAC,IACvB,CAACnK,KAAK,CAACJ,IAAI,CAAC2L,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAF,YAAY,GAAGrL,KAAK,CAACJ,IAAI;IAC3B,CAAC,QAASI,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEA+G,aAAaA,CAACxI,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAGAsM,oBAAoBA,CAACtM,IAAY,EAAgB;IAAA,IAAA0M,iBAAA;IAC/C,QAAAA,iBAAA,GAAO,IAAI,CAACpK,UAAU,CAACtC,IAAI,CAAC,qBAArB0M,iBAAA,CAAuBrQ,UAAU;EAC1C;EAGAsQ,uBAAuBA,CAAC3M,IAAY,EAAgB;IAClD,MAAMqC,OAAO,GAAG,IAAI,CAACc,QAAQ,CAACnD,IAAI,CAAC;IACnC,OAAOqC,OAAO,oBAAPA,OAAO,CAAEhG,UAAU;EAC5B;EAEAuQ,aAAaA,CAAC5M,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACwI,aAAa,CAACxI,IAAI,CAAC;EACnC;EAQAsF,UAAUA,CACRtF,IAAY,EACZ+K,IAA0D,EAC1D;IACA,IAAI,CAAC/K,IAAI,EAAE,OAAO,KAAK;IACvB,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAAC0L,aAAa,CAAC5M,IAAI,CAAC,EAAE;QAC7B,OAAO,IAAI;MACb;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAG9B,IAAIoI,SAAS;IACb,IAAIgD,MAAM;IACV,IAAI,OAAO9B,IAAI,KAAK,QAAQ,EAAE;MAC5BlB,SAAS,GAAGkB,IAAI,CAAClB,SAAS;MAC1BgD,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;IACtB,CAAC,MAAM,IAAI,OAAO9B,IAAI,KAAK,SAAS,EAAE;MACpClB,SAAS,GAAGkB,IAAI;IAClB;IAEA,IAAI,CAAC8B,MAAM,IAAI,IAAI,CAAClE,MAAM,CAAC3I,IAAI,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAAC6J,SAAS,IAAIhG,KAAK,CAACK,OAAO,CAAC4I,QAAQ,CAAC9M,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAI,CAAC6J,SAAS,IAAIhG,KAAK,CAACkJ,gBAAgB,CAACD,QAAQ,CAAC9M,IAAI,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd;EAEAgN,gBAAgBA,CACdhN,IAAY,EACZ+K,IAAgD,EAChD;IAAA,IAAAkC,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAACxL,MAAM,qBAAXwL,YAAA,CAAa3H,UAAU,CAACtF,IAAI,EAAE+K,IAAI,CAAC;EAC5C;EAMAmC,aAAaA,CAAClN,IAAY,EAAEkB,KAAY,EAAE;IACxC,MAAMiM,IAAI,GAAG,IAAI,CAAC7K,UAAU,CAACtC,IAAI,CAAC;IAClC,IAAImN,IAAI,EAAE;MACRA,IAAI,CAACjM,KAAK,CAACkM,gBAAgB,CAACpN,IAAI,CAAC;MACjCmN,IAAI,CAACjM,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGmN,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAACpN,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAEAqN,aAAaA,CAACrN,IAAY,EAAE;IAAA,IAAAsN,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAChL,UAAU,CAACtC,IAAI,CAAC,aAArBsN,iBAAA,CAAuBpM,KAAK,CAACkM,gBAAgB,CAACpN,IAAI,CAAC;IAGnD,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACiD,IAAI,CAACnE,IAAI,CAAC,EAAE;QACpBkB,KAAK,CAACiD,IAAI,CAACnE,IAAI,CAAC,GAAG,KAAK;MAC1B;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAYA8L,cAAcA,CACZC,IAAkD,GAAGlN,EAAE,IACrD,IAAI,CAACV,IAAI,CAAC;IAAEU;EAAG,CAAC,CAAC,EACnB;IACA,IAAI,CAAC+J,KAAK,CAAC,CAAC;IAEZ,MAAMoD,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,KAAK,MAAM1N,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAAC,IAAI,CAACQ,QAAQ,CAAC,EAAE;MAC7C,MAAMd,OAAO,GAAG,IAAI,CAACc,QAAQ,CAACnD,IAAI,CAAC;MACnC,IAAI,CAACqC,OAAO,EAAE;MACd,MAAM;QAAEvB;MAAK,CAAC,GAAGuB,OAAO;MACxB,IAAI,CAACvB,IAAI,CAAC6M,oBAAoB,CAAC,CAAC,EAAE;MAClC,MAAM;QAAElM,MAAM;QAAEqD;MAAW,CAAC,GAAGhE,IAAI;MAEnC,IAAIW,MAAM,CAAC2E,IAAI,KAAK,KAAK,IAAIqH,IAAI,CAACG,GAAG,CAACnM,MAAM,CAAC,EAAE;MAC/CgM,IAAI,CAACI,GAAG,CAAC/M,IAAI,CAACW,MAAM,CAAC;MAErB,IAAIqM,OAAO;MACX,MAAM1D,IAAI,GAAG,EAAE;MACf,KAAK,MAAM5H,IAAI,IAAIf,MAAM,CAACgB,YAAY,EAAE;QAAA,IAAAsL,QAAA;QACtC,CAAAA,QAAA,GAAAD,OAAO,YAAAC,QAAA,GAAPD,OAAO,GAAKtL,IAAI,CAAClC,EAAE;QACnB,IAAIkC,IAAI,CAAC4H,IAAI,EAAE;UACbA,IAAI,CAACxK,IAAI,CAAC3D,oBAAoB,CAAC,GAAG,EAAEuG,IAAI,CAAClC,EAAE,EAAEkC,IAAI,CAAC4H,IAAI,CAAC,CAAC;QAC1D;QAEA,MAAMpC,GAAG,GAAGtF,MAAM,CAACC,IAAI,CAACvG,qBAAqB,CAACoG,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvE,KAAK,MAAMxC,IAAI,IAAIgI,GAAG,EAAE;UACtBwF,IAAI,CAACnR,UAAU,CAAC2D,IAAI,CAAC,EAAEwC,IAAI,CAAC4H,IAAI,IAAI,IAAI,CAAC;QAC3C;MACF;MAGA,IAAItF,UAAU,CAACA,UAAU,CAACkJ,KAAK,CAAC;QAAE3N,IAAI,EAAEoB;MAAO,CAAC,CAAC,EAAE;QACjDqD,UAAU,CAACmJ,WAAW,CAACH,OAAO,CAAC;MACjC,CAAC,MAAM,IAAI1D,IAAI,CAAC5K,MAAM,KAAK,CAAC,EAAE;QAC5BsF,UAAU,CAACoJ,MAAM,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAMC,IAAI,GAAG/D,IAAI,CAAC5K,MAAM,KAAK,CAAC,GAAG4K,IAAI,CAAC,CAAC,CAAC,GAAGpL,kBAAkB,CAACoL,IAAI,CAAC;QACnE,IAAItF,UAAU,CAACA,UAAU,CAACsJ,cAAc,CAAC;UAAEhE,IAAI,EAAE3I;QAAO,CAAC,CAAC,EAAE;UAC1DqD,UAAU,CAACmJ,WAAW,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLrJ,UAAU,CAACmJ,WAAW,CAAChQ,mBAAmB,CAACkQ,IAAI,CAAC,CAAC;QACnD;MACF;IACF;EACF;AACF;AAACE,OAAA,CAAAC,OAAA,GAAAzK,KAAA;AAz5BKA,KAAK,CA2CFK,OAAO,GAAGxB,MAAM,CAACC,IAAI,CAACuB,QAAO,CAACqK,OAAO,CAAC;AA3CzC1K,KAAK,CAiDFkJ,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC;AA02B1B;EAG7ClJ,KAAK,CAAC2K,SAAS,CAACC,cAAc,GAAG,SAASA,cAAcA,CACtDC,GAAqC,EACrChI,OAAwB,EACxBC,OAAwB,EACxB9G,KAAc,EACd;IACA,IAAI6O,GAAG,CAAChI,OAAO,CAAC,EAAE;MAChBgI,GAAG,CAAC/H,OAAO,CAAC,GAAG9G,KAAK;MACpB6O,GAAG,CAAChI,OAAO,CAAC,GAAG,IAAI;IACrB;EACF,CAAC;EAcD7C,KAAK,CAAC2K,SAAS,CAAC3D,QAAQ,GAAG,UAEzB3L,IAAS,EACT6L,IAAS,EACTjJ,KAAS,EACT;IACA,IAAA+I,cAAQ,EAAC3L,IAAI,EAAE6L,IAAI,EAAE,IAAI,EAAEjJ,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C,CAAC;EAMD+C,KAAK,CAAC2K,SAAS,CAACG,YAAY,GAAG,SAASA,YAAYA,CAClD3O,IAAY,EACZoF,CAAS,EACT;IACA,IAAI9E,EAAE,GAAGN,IAAI;IACb,IAAIoF,CAAC,GAAG,CAAC,EAAE9E,EAAE,IAAI8E,CAAC;IAClB,OAAO,IAAI9E,EAAE,EAAE;EACjB,CAAC;EAIDuD,KAAK,CAAC2K,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAExC1P,IAAY,EACZkG,CAAoB,EACpByJ,mBAAoC,EACpC;IACA,IAAI7R,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,YAAPA,OAAO,CAAE2D,QAAQ,IAAI3D,OAAO,CAACvB,IAAI,CAACgO,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAO5P,IAAI;MACb;IACF;IAEA,IAAI5C,iBAAiB,CAAC4C,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO9D,cAAc,CACnBiC,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC9B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAAC6C,IAAI,CACP,CAAC;IACH;IAEA,IAAI6P,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC9P,IAAI,CAAC;IACnB,IAAIkG,CAAC,KAAK,IAAI,EAAE;MAEd2J,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAO3J,CAAC,KAAK,QAAQ,EAAE;MAChC4J,IAAI,CAACpP,IAAI,CAACxB,cAAc,CAACgH,CAAC,CAAC,CAAC;MAG5B2J,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACnO,IAAI,CAACwF,GAAG,CAAC4I,SAAS,CAACH,UAAU,CAAC,CAAC;MACjDA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAO7S,cAAc,CAAC,IAAI,CAAC4E,IAAI,CAACwF,GAAG,CAAC4I,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAClE,CAAC;EAMDnL,KAAK,CAAC2K,SAAS,CAACW,oBAAoB,GAAG,SAASA,oBAAoBA,CAClE,GAAGC,KAAe,EACO;IACzB,MAAMpH,GAAG,GAAGtF,MAAM,CAAC4H,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMlE,IAAI,IAAIgJ,KAAK,EAAE;MACxB,IAAIlO,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMlB,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;UAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;UACpC,IAAIqC,OAAO,CAAC+D,IAAI,KAAKA,IAAI,EAAE4B,GAAG,CAAChI,IAAI,CAAC,GAAGqC,OAAO;QAChD;QACAnB,KAAK,GAAGA,KAAK,CAACO,MAAM;MACtB,CAAC,QAAQP,KAAK;IAChB;IAEA,OAAO8G,GAAG;EACZ,CAAC;EAEDtF,MAAM,CAAC2M,gBAAgB,CAACxL,KAAK,CAAC2K,SAAS,EAAE;IACvCc,WAAW,EAAE;MACXC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBxO,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAACW,MAAM;MACzB;IACF,CAAC;IACD6E,GAAG,EAAE;MACHiJ,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBxO,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAACwF,GAAG;MACtB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}