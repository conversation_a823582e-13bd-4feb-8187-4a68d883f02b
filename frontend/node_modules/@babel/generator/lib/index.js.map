{"version": 3, "names": ["_sourceMap", "require", "_printer", "normalizeOptions", "code", "opts", "format", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "shouldPrintComment", "retainLines", "retainFunctionParens", "comments", "compact", "minified", "concise", "indent", "adjustMultilineComment", "style", "jsescOption", "Object", "assign", "quotes", "wrap", "minimal", "topicToken", "importAttributesKeyword", "_opts$recordAndTupleS", "decoratorsBeforeExport", "json", "jsonCompatibleStrings", "recordAndTupleSyntaxType", "value", "includes", "length", "console", "error", "filename", "undefined", "exports", "CodeGenerator", "constructor", "ast", "_ast", "_format", "_map", "sourceMaps", "SourceMap", "generate", "printer", "Printer", "map"], "sources": ["../src/index.ts"], "sourcesContent": ["import SourceMap from \"./source-map.ts\";\nimport Printer from \"./printer.ts\";\nimport type * as t from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\nimport type { Format } from \"./printer.ts\";\nimport type {\n  EncodedSourceMap,\n  DecodedSourceMap,\n  Mapping,\n} from \"@jridgewell/gen-mapping\";\n\n/**\n * Normalize generator options, setting defaults.\n *\n * - Detects code indentation.\n * - If `opts.compact = \"auto\"` and the code is over 500KB, `compact` will be set to `true`.\n */\n\nfunction normalizeOptions(\n  code: string | { [filename: string]: string },\n  opts: GeneratorOptions,\n): Format {\n  const format: Format = {\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    shouldPrintComment: opts.shouldPrintComment,\n    retainLines: opts.retainLines,\n    retainFunctionParens: opts.retainFunctionParens,\n    comments: opts.comments == null || opts.comments,\n    compact: opts.compact,\n    minified: opts.minified,\n    concise: opts.concise,\n    indent: {\n      adjustMultilineComment: true,\n      style: \"  \",\n    },\n    jsescOption: {\n      quotes: \"double\",\n      wrap: true,\n      minimal: process.env.BABEL_8_BREAKING ? true : false,\n      ...opts.jsescOption,\n    },\n    topicToken: opts.topicToken,\n    importAttributesKeyword: opts.importAttributesKeyword,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    format.decoratorsBeforeExport = opts.decoratorsBeforeExport;\n    format.jsescOption.json = opts.jsonCompatibleStrings;\n    format.recordAndTupleSyntaxType = opts.recordAndTupleSyntaxType ?? \"hash\";\n  }\n\n  if (format.minified) {\n    format.compact = true;\n\n    format.shouldPrintComment =\n      format.shouldPrintComment || (() => format.comments);\n  } else {\n    format.shouldPrintComment =\n      format.shouldPrintComment ||\n      (value =>\n        format.comments ||\n        value.includes(\"@license\") ||\n        value.includes(\"@preserve\"));\n  }\n\n  if (format.compact === \"auto\") {\n    format.compact = typeof code === \"string\" && code.length > 500_000; // 500KB\n\n    if (format.compact) {\n      console.error(\n        \"[BABEL] Note: The code generator has deoptimised the styling of \" +\n          `${opts.filename} as it exceeds the max of ${\"500KB\"}.`,\n      );\n    }\n  }\n\n  if (format.compact) {\n    format.indent.adjustMultilineComment = false;\n  }\n\n  const { auxiliaryCommentBefore, auxiliaryCommentAfter, shouldPrintComment } =\n    format;\n\n  if (auxiliaryCommentBefore && !shouldPrintComment(auxiliaryCommentBefore)) {\n    format.auxiliaryCommentBefore = undefined;\n  }\n  if (auxiliaryCommentAfter && !shouldPrintComment(auxiliaryCommentAfter)) {\n    format.auxiliaryCommentAfter = undefined;\n  }\n\n  return format;\n}\n\nexport interface GeneratorOptions {\n  /**\n   * Optional string to add as a block comment at the start of the output file.\n   */\n  auxiliaryCommentBefore?: string;\n\n  /**\n   * Optional string to add as a block comment at the end of the output file.\n   */\n  auxiliaryCommentAfter?: string;\n\n  /**\n   * Function that takes a comment (as a string) and returns true if the comment should be included in the output.\n   * By default, comments are included if `opts.comments` is `true` or if `opts.minified` is `false` and the comment\n   * contains `@preserve` or `@license`.\n   */\n  shouldPrintComment?(comment: string): boolean;\n\n  /**\n   * Attempt to use the same line numbers in the output code as in the source code (helps preserve stack traces).\n   * Defaults to `false`.\n   */\n  retainLines?: boolean;\n\n  /**\n   * Retain parens around function expressions (could be used to change engine parsing behavior)\n   * Defaults to `false`.\n   */\n  retainFunctionParens?: boolean;\n\n  /**\n   * Should comments be included in output? Defaults to `true`.\n   */\n  comments?: boolean;\n\n  /**\n   * Set to true to avoid adding whitespace for formatting. Defaults to the value of `opts.minified`.\n   */\n  compact?: boolean | \"auto\";\n\n  /**\n   * Should the output be minified. Defaults to `false`.\n   */\n  minified?: boolean;\n\n  /**\n   * Set to true to reduce whitespace (but not as much as opts.compact). Defaults to `false`.\n   */\n  concise?: boolean;\n\n  /**\n   * Used in warning messages\n   */\n  filename?: string;\n\n  /**\n   * Enable generating source maps. Defaults to `false`.\n   */\n  sourceMaps?: boolean;\n\n  inputSourceMap?: any;\n\n  /**\n   * A root for all relative URLs in the source map.\n   */\n  sourceRoot?: string;\n\n  /**\n   * The filename for the source code (i.e. the code in the `code` argument).\n   * This will only be used if `code` is a string.\n   */\n  sourceFileName?: string;\n\n  /**\n   * Set to true to run jsesc with \"json\": true to print \"\\u00A9\" vs. \"©\";\n   * @deprecated use `jsescOptions: { json: true }` instead\n   */\n  jsonCompatibleStrings?: boolean;\n\n  /**\n   * Set to true to enable support for experimental decorators syntax before\n   * module exports. If not specified, decorators will be printed in the same\n   * position as they were in the input source code.\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n\n  /**\n   * Options for outputting jsesc representation.\n   */\n  jsescOption?: jsescOptions;\n\n  /**\n   * For use with the recordAndTuple token.\n   * @deprecated It will be removed in Babel 8.\n   */\n  recordAndTupleSyntaxType?: \"bar\" | \"hash\";\n\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: \"%\" | \"#\" | \"@@\" | \"^^\" | \"^\";\n\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n}\n\nexport interface GeneratorResult {\n  code: string;\n  map: EncodedSourceMap | null;\n  decodedMap: DecodedSourceMap | undefined;\n  rawMappings: Mapping[] | undefined;\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * We originally exported the Generator class above, but to make it extra clear that it is a private API,\n   * we have moved that to an internal class instance and simplified the interface to the two public methods\n   * that we wish to support.\n   */\n\n  // eslint-disable-next-line no-restricted-globals\n  exports.CodeGenerator = class CodeGenerator {\n    private _ast: t.Node;\n    private _format: Format | undefined;\n    private _map: SourceMap | null;\n    constructor(ast: t.Node, opts: GeneratorOptions = {}, code?: string) {\n      this._ast = ast;\n      this._format = normalizeOptions(code, opts);\n      this._map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n    }\n    generate(): GeneratorResult {\n      const printer = new Printer(this._format, this._map);\n\n      return printer.generate(this._ast);\n    }\n  };\n}\n\n/**\n * Turns an AST into code, maintaining sourcemaps, user preferences, and valid output.\n * @param ast - the abstract syntax tree from which to generate output code.\n * @param opts - used for specifying options for code generation.\n * @param code - the original source code, used for source maps.\n * @returns - an object containing the output code and source map.\n */\nexport default function generate(\n  ast: t.Node,\n  opts: GeneratorOptions = {},\n  code?: string | { [filename: string]: string },\n): GeneratorResult {\n  const format = normalizeOptions(code, opts);\n  const map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n\n  const printer = new Printer(format, map);\n\n  return printer.generate(ast);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAiBA,SAASE,gBAAgBA,CACvBC,IAA6C,EAC7CC,IAAsB,EACd;EACR,MAAMC,MAAc,GAAG;IACrBC,sBAAsB,EAAEF,IAAI,CAACE,sBAAsB;IACnDC,qBAAqB,EAAEH,IAAI,CAACG,qBAAqB;IACjDC,kBAAkB,EAAEJ,IAAI,CAACI,kBAAkB;IAC3CC,WAAW,EAAEL,IAAI,CAACK,WAAW;IAC7BC,oBAAoB,EAAEN,IAAI,CAACM,oBAAoB;IAC/CC,QAAQ,EAAEP,IAAI,CAACO,QAAQ,IAAI,IAAI,IAAIP,IAAI,CAACO,QAAQ;IAChDC,OAAO,EAAER,IAAI,CAACQ,OAAO;IACrBC,QAAQ,EAAET,IAAI,CAACS,QAAQ;IACvBC,OAAO,EAAEV,IAAI,CAACU,OAAO;IACrBC,MAAM,EAAE;MACNC,sBAAsB,EAAE,IAAI;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAwC;IAAK,GACjDnB,IAAI,CAACc,WAAW,CACpB;IACDM,UAAU,EAAEpB,IAAI,CAACoB,UAAU;IAC3BC,uBAAuB,EAAErB,IAAI,CAACqB;EAChC,CAAC;EAEkC;IAAA,IAAAC,qBAAA;IACjCrB,MAAM,CAACsB,sBAAsB,GAAGvB,IAAI,CAACuB,sBAAsB;IAC3DtB,MAAM,CAACa,WAAW,CAACU,IAAI,GAAGxB,IAAI,CAACyB,qBAAqB;IACpDxB,MAAM,CAACyB,wBAAwB,IAAAJ,qBAAA,GAAGtB,IAAI,CAAC0B,wBAAwB,YAAAJ,qBAAA,GAAI,MAAM;EAC3E;EAEA,IAAIrB,MAAM,CAACQ,QAAQ,EAAE;IACnBR,MAAM,CAACO,OAAO,GAAG,IAAI;IAErBP,MAAM,CAACG,kBAAkB,GACvBH,MAAM,CAACG,kBAAkB,KAAK,MAAMH,MAAM,CAACM,QAAQ,CAAC;EACxD,CAAC,MAAM;IACLN,MAAM,CAACG,kBAAkB,GACvBH,MAAM,CAACG,kBAAkB,KACxBuB,KAAK,IACJ1B,MAAM,CAACM,QAAQ,IACfoB,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC1BD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAClC;EAEA,IAAI3B,MAAM,CAACO,OAAO,KAAK,MAAM,EAAE;IAC7BP,MAAM,CAACO,OAAO,GAAG,OAAOT,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC8B,MAAM,GAAG,MAAO;IAElE,IAAI5B,MAAM,CAACO,OAAO,EAAE;MAClBsB,OAAO,CAACC,KAAK,CACX,kEAAkE,GAChE,GAAG/B,IAAI,CAACgC,QAAQ,6BAA6B,OAAO,GACxD,CAAC;IACH;EACF;EAEA,IAAI/B,MAAM,CAACO,OAAO,EAAE;IAClBP,MAAM,CAACU,MAAM,CAACC,sBAAsB,GAAG,KAAK;EAC9C;EAEA,MAAM;IAAEV,sBAAsB;IAAEC,qBAAqB;IAAEC;EAAmB,CAAC,GACzEH,MAAM;EAER,IAAIC,sBAAsB,IAAI,CAACE,kBAAkB,CAACF,sBAAsB,CAAC,EAAE;IACzED,MAAM,CAACC,sBAAsB,GAAG+B,SAAS;EAC3C;EACA,IAAI9B,qBAAqB,IAAI,CAACC,kBAAkB,CAACD,qBAAqB,CAAC,EAAE;IACvEF,MAAM,CAACE,qBAAqB,GAAG8B,SAAS;EAC1C;EAEA,OAAOhC,MAAM;AACf;AA0H+C;EAQ7CiC,OAAO,CAACC,aAAa,GAAG,MAAMA,aAAa,CAAC;IAI1CC,WAAWA,CAACC,GAAW,EAAErC,IAAsB,GAAG,CAAC,CAAC,EAAED,IAAa,EAAE;MAAA,KAH7DuC,IAAI;MAAA,KACJC,OAAO;MAAA,KACPC,IAAI;MAEV,IAAI,CAACF,IAAI,GAAGD,GAAG;MACf,IAAI,CAACE,OAAO,GAAGzC,gBAAgB,CAACC,IAAI,EAAEC,IAAI,CAAC;MAC3C,IAAI,CAACwC,IAAI,GAAGxC,IAAI,CAACyC,UAAU,GAAG,IAAIC,kBAAS,CAAC1C,IAAI,EAAED,IAAI,CAAC,GAAG,IAAI;IAChE;IACA4C,QAAQA,CAAA,EAAoB;MAC1B,MAAMC,OAAO,GAAG,IAAIC,gBAAO,CAAC,IAAI,CAACN,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC;MAEpD,OAAOI,OAAO,CAACD,QAAQ,CAAC,IAAI,CAACL,IAAI,CAAC;IACpC;EACF,CAAC;AACH;AASe,SAASK,QAAQA,CAC9BN,GAAW,EACXrC,IAAsB,GAAG,CAAC,CAAC,EAC3BD,IAA8C,EAC7B;EACjB,MAAME,MAAM,GAAGH,gBAAgB,CAACC,IAAI,EAAEC,IAAI,CAAC;EAC3C,MAAM8C,GAAG,GAAG9C,IAAI,CAACyC,UAAU,GAAG,IAAIC,kBAAS,CAAC1C,IAAI,EAAED,IAAI,CAAC,GAAG,IAAI;EAE9D,MAAM6C,OAAO,GAAG,IAAIC,gBAAO,CAAC5C,MAAM,EAAE6C,GAAG,CAAC;EAExC,OAAOF,OAAO,CAACD,QAAQ,CAACN,GAAG,CAAC;AAC9B", "ignoreList": []}