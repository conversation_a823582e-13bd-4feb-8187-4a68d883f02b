{"version": 3, "names": ["_t", "require", "isExportDefaultDeclaration", "isExportNamedDeclaration", "ClassDeclaration", "node", "parent", "inExport", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "declare", "word", "space", "abstract", "id", "print", "typeParameters", "superClass", "superTypeParameters", "implements", "printList", "body", "ClassBody", "token", "length", "newline", "exit", "enterDelimited", "printSequence", "indent", "endsWith", "rightBrace", "ClassProperty", "_node$key$loc", "endLine", "key", "loc", "end", "line", "catchUp", "tsPrintClassMemberModifiers", "computed", "_variance", "optional", "definite", "typeAnnotation", "value", "semicolon", "ClassAccessorProperty", "_node$key$loc2", "ClassPrivateProperty", "static", "ClassMethod", "_classMethodHead", "ClassPrivateMethod", "_node$key$loc3", "_methodHead", "StaticBlock"], "sources": ["../../src/generators/classes.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nexport function ClassDeclaration(\n  this: Printer,\n  node: t.ClassDeclaration,\n  parent: t.Node,\n) {\n  const inExport =\n    isExportDefaultDeclaration(parent) || isExportNamedDeclaration(parent);\n\n  if (\n    !inExport ||\n    !this._shouldPrintDecoratorsBeforeExport(\n      parent as t.ExportDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    this.printJoin(node.decorators);\n  }\n\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (node.abstract) {\n    // TS\n    this.word(\"abstract\");\n    this.space();\n  }\n\n  this.word(\"class\");\n\n  if (node.id) {\n    this.space();\n    this.print(node.id);\n  }\n\n  this.print(node.typeParameters);\n\n  if (node.superClass) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.superClass);\n    this.print(node.superTypeParameters);\n  }\n\n  if (node.implements) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements);\n  }\n\n  this.space();\n  this.print(node.body);\n}\n\nexport { ClassDeclaration as ClassExpression };\n\nexport function ClassBody(this: Printer, node: t.ClassBody) {\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n\n    const exit = this.enterDelimited();\n    this.printSequence(node.body, { indent: true });\n    exit();\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.rightBrace(node);\n  }\n}\n\nexport function ClassProperty(this: Printer, node: t.ClassProperty) {\n  this.printJoin(node.decorators);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key);\n    this.token(\"]\");\n  } else {\n    this._variance(node);\n    this.print(node.key);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassAccessorProperty(\n  this: Printer,\n  node: t.ClassAccessorProperty,\n) {\n  this.printJoin(node.decorators);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  // TS does not support class accessor property yet\n  this.tsPrintClassMemberModifiers(node);\n\n  this.word(\"accessor\", true);\n  this.space();\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key);\n    this.token(\"]\");\n  } else {\n    // Todo: Flow does not support class accessor property yet.\n    this._variance(node);\n    this.print(node.key);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassPrivateProperty(\n  this: Printer,\n  node: t.ClassPrivateProperty,\n) {\n  this.printJoin(node.decorators);\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.key);\n  this.print(node.typeAnnotation);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value);\n  }\n  this.semicolon();\n}\n\nexport function ClassMethod(this: Printer, node: t.ClassMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body);\n}\n\nexport function ClassPrivateMethod(this: Printer, node: t.ClassPrivateMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body);\n}\n\nexport function _classMethodHead(\n  this: Printer,\n  node: t.ClassMethod | t.ClassPrivateMethod | t.TSDeclareMethod,\n) {\n  this.printJoin(node.decorators);\n\n  // catch up to method key, avoid line break\n  // between member modifiers/method heads and the method key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n  this._methodHead(node);\n}\n\nexport function StaticBlock(this: Printer, node: t.StaticBlock) {\n  this.word(\"static\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body, { indent: true });\n    this.rightBrace(node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,0BAA0B;EAC1BC;AAAwB,IAAAH,EAAA;AAQnB,SAASI,gBAAgBA,CAE9BC,IAAwB,EACxBC,MAAc,EACd;EACA,MAAMC,QAAQ,GACZL,0BAA0B,CAACI,MAAM,CAAC,IAAIH,wBAAwB,CAACG,MAAM,CAAC;EAExE,IACE,CAACC,QAAQ,IACT,CAAC,IAAI,CAACC,kCAAkC,CACtCF,MACF,CAAC,EACD;IACA,IAAI,CAACG,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EACjC;EAEA,IAAIL,IAAI,CAACM,OAAO,EAAE;IAEhB,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAIR,IAAI,CAACS,QAAQ,EAAE;IAEjB,IAAI,CAACF,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIP,IAAI,CAACU,EAAE,EAAE;IACX,IAAI,CAACF,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACU,EAAE,CAAC;EACrB;EAEA,IAAI,CAACC,KAAK,CAACX,IAAI,CAACY,cAAc,CAAC;EAE/B,IAAIZ,IAAI,CAACa,UAAU,EAAE;IACnB,IAAI,CAACL,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACa,UAAU,CAAC;IAC3B,IAAI,CAACF,KAAK,CAACX,IAAI,CAACc,mBAAmB,CAAC;EACtC;EAEA,IAAId,IAAI,CAACe,UAAU,EAAE;IACnB,IAAI,CAACP,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;IACvB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACQ,SAAS,CAAChB,IAAI,CAACe,UAAU,CAAC;EACjC;EAEA,IAAI,CAACP,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAIO,SAASC,SAASA,CAAgBlB,IAAiB,EAAE;EAC1D,IAAI,CAACmB,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IAEd,MAAMC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAClC,IAAI,CAACC,aAAa,CAACxB,IAAI,CAACiB,IAAI,EAAE;MAAEQ,MAAM,EAAE;IAAK,CAAC,CAAC;IAC/CH,IAAI,CAAC,CAAC;IAEN,IAAI,CAAC,IAAI,CAACI,QAAQ,GAAmB,CAAC,EAAE,IAAI,CAACL,OAAO,CAAC,CAAC;IAEtD,IAAI,CAACM,UAAU,CAAC3B,IAAI,CAAC;EACvB;AACF;AAEO,SAAS4B,aAAaA,CAAgB5B,IAAqB,EAAE;EAAA,IAAA6B,aAAA;EAClE,IAAI,CAACzB,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAI/B,MAAMyB,OAAO,IAAAD,aAAA,GAAG7B,IAAI,CAAC+B,GAAG,CAACC,GAAG,cAAAH,aAAA,GAAZA,aAAA,CAAcI,GAAG,qBAAjBJ,aAAA,CAAmBK,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAElC,IAAI,CAACM,2BAA2B,CAACpC,IAAI,CAAC;EAEtC,IAAIA,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC+B,GAAG,CAAC;IACpB,IAAI,CAACZ,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACmB,SAAS,CAACtC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC+B,GAAG,CAAC;EACtB;EAGA,IAAI/B,IAAI,CAACuC,QAAQ,EAAE;IACjB,IAAI,CAACpB,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACwC,QAAQ,EAAE;IACjB,IAAI,CAACrB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAACyC,cAAc,CAAC;EAC/B,IAAIzC,IAAI,CAAC0C,KAAK,EAAE;IACd,IAAI,CAAClC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC0C,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASC,qBAAqBA,CAEnC5C,IAA6B,EAC7B;EAAA,IAAA6C,cAAA;EACA,IAAI,CAACzC,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAI/B,MAAMyB,OAAO,IAAAe,cAAA,GAAG7C,IAAI,CAAC+B,GAAG,CAACC,GAAG,cAAAa,cAAA,GAAZA,cAAA,CAAcZ,GAAG,qBAAjBY,cAAA,CAAmBX,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAGlC,IAAI,CAACM,2BAA2B,CAACpC,IAAI,CAAC;EAEtC,IAAI,CAACO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,IAAIR,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC+B,GAAG,CAAC;IACpB,IAAI,CAACZ,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IAAI,CAACmB,SAAS,CAACtC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC+B,GAAG,CAAC;EACtB;EAGA,IAAI/B,IAAI,CAACuC,QAAQ,EAAE;IACjB,IAAI,CAACpB,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACwC,QAAQ,EAAE;IACjB,IAAI,CAACrB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAACyC,cAAc,CAAC;EAC/B,IAAIzC,IAAI,CAAC0C,KAAK,EAAE;IACd,IAAI,CAAClC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC0C,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASG,oBAAoBA,CAElC9C,IAA4B,EAC5B;EACA,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAC/B,IAAIL,IAAI,CAAC+C,MAAM,EAAE;IACf,IAAI,CAACxC,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC+B,GAAG,CAAC;EACpB,IAAI,CAACpB,KAAK,CAACX,IAAI,CAACyC,cAAc,CAAC;EAC/B,IAAIzC,IAAI,CAAC0C,KAAK,EAAE;IACd,IAAI,CAAClC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC0C,KAAK,CAAC;EACxB;EACA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASK,WAAWA,CAAgBhD,IAAmB,EAAE;EAC9D,IAAI,CAACiD,gBAAgB,CAACjD,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAEO,SAASiC,kBAAkBA,CAAgBlD,IAA0B,EAAE;EAC5E,IAAI,CAACiD,gBAAgB,CAACjD,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,CAAC;AACvB;AAEO,SAASgC,gBAAgBA,CAE9BjD,IAA8D,EAC9D;EAAA,IAAAmD,cAAA;EACA,IAAI,CAAC/C,SAAS,CAACJ,IAAI,CAACK,UAAU,CAAC;EAI/B,MAAMyB,OAAO,IAAAqB,cAAA,GAAGnD,IAAI,CAAC+B,GAAG,CAACC,GAAG,cAAAmB,cAAA,GAAZA,cAAA,CAAclB,GAAG,qBAAjBkB,cAAA,CAAmBjB,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAElC,IAAI,CAACM,2BAA2B,CAACpC,IAAI,CAAC;EACtC,IAAI,CAACoD,WAAW,CAACpD,IAAI,CAAC;AACxB;AAEO,SAASqD,WAAWA,CAAgBrD,IAAmB,EAAE;EAC9D,IAAI,CAACO,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACW,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAACG,aAAa,CAACxB,IAAI,CAACiB,IAAI,EAAE;MAAEQ,MAAM,EAAE;IAAK,CAAC,CAAC;IAC/C,IAAI,CAACE,UAAU,CAAC3B,IAAI,CAAC;EACvB;AACF", "ignoreList": []}