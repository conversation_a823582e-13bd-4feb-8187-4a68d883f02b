{"version": 3, "names": ["_t", "require", "_index", "isIdentifier", "_params", "node", "idNode", "parentNode", "print", "typeParameters", "nameInfo", "_getFuncIdName", "call", "sourceIdentifierName", "name", "pos", "token", "_parameters", "params", "noLineTerminator", "type", "returnType", "_noLineTerminator", "parameters", "exit", "enterDelimited", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "_param", "space", "parameter", "printJoin", "decorators", "optional", "typeAnnotation", "_methodHead", "kind", "key", "word", "async", "generator", "computed", "undefined", "_predicate", "noLineTerminatorAfter", "predicate", "_functionHead", "parent", "_endsWithInnerRaw", "id", "FunctionExpression", "body", "ArrowFunctionExpression", "firstParam", "format", "retainLines", "hasTypesOrComments", "printInnerComments", "tokenContext", "TokenContext", "arrowBody", "param", "_param$leadingComment", "_param$trailingCommen", "leadingComments", "trailingComments", "parentType", "left", "_id$loc", "_id$loc2", "loc", "start", "identifierName", "_id$loc3", "_id$loc4", "value"], "sources": ["../../src/generators/methods.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\nimport { isIdentifier, type ParentMaps } from \"@babel/types\";\nimport { TokenContext } from \"../node/index.ts\";\n\ntype ParentsOf<T extends t.Node> = ParentMaps[T[\"type\"]];\n\nexport function _params(\n  this: Printer,\n  node: t.Function | t.TSDeclareMethod | t.TSDeclareFunction,\n  idNode: t.Expression | t.PrivateName,\n  parentNode: ParentsOf<typeof node>,\n) {\n  this.print(node.typeParameters);\n\n  const nameInfo = _getFuncIdName.call(this, idNode, parentNode);\n  if (nameInfo) {\n    this.sourceIdentifierName(nameInfo.name, nameInfo.pos);\n  }\n\n  this.token(\"(\");\n  this._parameters(node.params);\n  this.token(\")\");\n\n  const noLineTerminator = node.type === \"ArrowFunctionExpression\";\n  this.print(node.returnType, noLineTerminator);\n\n  this._noLineTerminator = noLineTerminator;\n}\n\nexport function _parameters(this: Printer, parameters: t.Function[\"params\"]) {\n  const exit = this.enterDelimited();\n\n  const paramLength = parameters.length;\n  for (let i = 0; i < paramLength; i++) {\n    this._param(parameters[i]);\n\n    if (i < parameters.length - 1) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n\n  exit();\n}\n\nexport function _param(\n  this: Printer,\n  parameter: t.Identifier | t.RestElement | t.Pattern | t.TSParameterProperty,\n) {\n  this.printJoin(parameter.decorators);\n  this.print(parameter);\n  if (\n    // @ts-expect-error optional is not in TSParameterProperty\n    parameter.optional\n  ) {\n    this.token(\"?\"); // TS / flow\n  }\n\n  this.print(\n    // @ts-expect-error typeAnnotation is not in TSParameterProperty\n    parameter.typeAnnotation,\n  ); // TS / flow\n}\n\nexport function _methodHead(this: Printer, node: t.Method | t.TSDeclareMethod) {\n  const kind = node.kind;\n  const key = node.key;\n\n  if (kind === \"get\" || kind === \"set\") {\n    this.word(kind);\n    this.space();\n  }\n\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  if (\n    kind === \"method\" ||\n    // @ts-expect-error Fixme: kind: \"init\" is not defined\n    kind === \"init\"\n  ) {\n    if (node.generator) {\n      this.token(\"*\");\n    }\n  }\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(key);\n    this.token(\"]\");\n  } else {\n    this.print(key);\n  }\n\n  if (\n    // @ts-expect-error optional is not in ObjectMethod\n    node.optional\n  ) {\n    // TS\n    this.token(\"?\");\n  }\n\n  this._params(\n    node,\n    node.computed && node.key.type !== \"StringLiteral\" ? undefined : node.key,\n    undefined,\n  );\n}\n\nexport function _predicate(\n  this: Printer,\n  node:\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.ArrowFunctionExpression,\n  noLineTerminatorAfter?: boolean,\n) {\n  if (node.predicate) {\n    if (!node.returnType) {\n      this.token(\":\");\n    }\n    this.space();\n    this.print(node.predicate, noLineTerminatorAfter);\n  }\n}\n\nexport function _functionHead(\n  this: Printer,\n  node: t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\");\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.space();\n  }\n  this.word(\"function\");\n  if (node.generator) {\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.token(\"*\");\n  }\n\n  this.space();\n  if (node.id) {\n    this.print(node.id);\n  }\n\n  this._params(node, node.id, parent);\n  if (node.type !== \"TSDeclareFunction\") {\n    this._predicate(node);\n  }\n}\n\nexport function FunctionExpression(\n  this: Printer,\n  node: t.FunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  this._functionHead(node, parent);\n  this.space();\n  this.print(node.body);\n}\n\nexport { FunctionExpression as FunctionDeclaration };\n\nexport function ArrowFunctionExpression(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  // Try to avoid printing parens in simple cases, but only if we're pretty\n  // sure that they aren't needed by type annotations or potential newlines.\n  let firstParam;\n  if (\n    !this.format.retainLines &&\n    node.params.length === 1 &&\n    isIdentifier((firstParam = node.params[0])) &&\n    !hasTypesOrComments(node, firstParam)\n  ) {\n    this.print(firstParam, true);\n  } else {\n    this._params(node, undefined, parent);\n  }\n\n  this._predicate(node, true);\n  this.space();\n  // When printing (x)/*1*/=>{}, we remove the parentheses\n  // and thus there aren't two contiguous inner tokens.\n  // We forcefully print inner comments here.\n  this.printInnerComments();\n  this.token(\"=>\");\n\n  this.space();\n\n  this.tokenContext |= TokenContext.arrowBody;\n  this.print(node.body);\n}\n\nfunction hasTypesOrComments(\n  node: t.ArrowFunctionExpression,\n  param: t.Identifier,\n): boolean {\n  return !!(\n    node.typeParameters ||\n    node.returnType ||\n    node.predicate ||\n    param.typeAnnotation ||\n    param.optional ||\n    // Flow does not support `foo /*: string*/ => {};`\n    param.leadingComments?.length ||\n    param.trailingComments?.length\n  );\n}\n\nfunction _getFuncIdName(\n  this: Printer,\n  idNode: t.Expression | t.PrivateName,\n  parent: ParentsOf<t.Function | t.TSDeclareMethod | t.TSDeclareFunction>,\n) {\n  let id: t.Expression | t.PrivateName | t.LVal = idNode;\n\n  if (!id && parent) {\n    const parentType = parent.type;\n\n    if (parentType === \"VariableDeclarator\") {\n      id = parent.id;\n    } else if (\n      parentType === \"AssignmentExpression\" ||\n      parentType === \"AssignmentPattern\"\n    ) {\n      id = parent.left;\n    } else if (\n      parentType === \"ObjectProperty\" ||\n      parentType === \"ClassProperty\"\n    ) {\n      if (!parent.computed || parent.key.type === \"StringLiteral\") {\n        id = parent.key;\n      }\n    } else if (\n      parentType === \"ClassPrivateProperty\" ||\n      parentType === \"ClassAccessorProperty\"\n    ) {\n      id = parent.key;\n    }\n  }\n\n  if (!id) return;\n\n  let nameInfo;\n\n  if (id.type === \"Identifier\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.loc?.identifierName || id.name,\n    };\n  } else if (id.type === \"PrivateName\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: \"#\" + id.id.name,\n    };\n  } else if (id.type === \"StringLiteral\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.value,\n    };\n  }\n\n  return nameInfo;\n}\n"], "mappings": ";;;;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EADvCE;AAAY,IAAAH,EAAA;AAKd,SAASI,OAAOA,CAErBC,IAA0D,EAC1DC,MAAoC,EACpCC,UAAkC,EAClC;EACA,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,cAAc,CAAC;EAE/B,MAAMC,QAAQ,GAAGC,cAAc,CAACC,IAAI,CAAC,IAAI,EAAEN,MAAM,EAAEC,UAAU,CAAC;EAC9D,IAAIG,QAAQ,EAAE;IACZ,IAAI,CAACG,oBAAoB,CAACH,QAAQ,CAACI,IAAI,EAAEJ,QAAQ,CAACK,GAAG,CAAC;EACxD;EAEA,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,WAAW,CAACZ,IAAI,CAACa,MAAM,CAAC;EAC7B,IAAI,CAACF,SAAK,GAAI,CAAC;EAEf,MAAMG,gBAAgB,GAAGd,IAAI,CAACe,IAAI,KAAK,yBAAyB;EAChE,IAAI,CAACZ,KAAK,CAACH,IAAI,CAACgB,UAAU,EAAEF,gBAAgB,CAAC;EAE7C,IAAI,CAACG,iBAAiB,GAAGH,gBAAgB;AAC3C;AAEO,SAASF,WAAWA,CAAgBM,UAAgC,EAAE;EAC3E,MAAMC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAElC,MAAMC,WAAW,GAAGH,UAAU,CAACI,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;IACpC,IAAI,CAACC,MAAM,CAACN,UAAU,CAACK,CAAC,CAAC,CAAC;IAE1B,IAAIA,CAAC,GAAGL,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACX,SAAK,GAAI,CAAC;MACf,IAAI,CAACc,KAAK,CAAC,CAAC;IACd;EACF;EAEAN,IAAI,CAAC,CAAC;AACR;AAEO,SAASK,MAAMA,CAEpBE,SAA2E,EAC3E;EACA,IAAI,CAACC,SAAS,CAACD,SAAS,CAACE,UAAU,CAAC;EACpC,IAAI,CAACzB,KAAK,CAACuB,SAAS,CAAC;EACrB,IAEEA,SAAS,CAACG,QAAQ,EAClB;IACA,IAAI,CAAClB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAERuB,SAAS,CAACI,cACZ,CAAC;AACH;AAEO,SAASC,WAAWA,CAAgB/B,IAAkC,EAAE;EAC7E,MAAMgC,IAAI,GAAGhC,IAAI,CAACgC,IAAI;EACtB,MAAMC,GAAG,GAAGjC,IAAI,CAACiC,GAAG;EAEpB,IAAID,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACf,IAAI,CAACP,KAAK,CAAC,CAAC;EACd;EAEA,IAAIzB,IAAI,CAACmC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAEA,IACEO,IAAI,KAAK,QAAQ,IAEjBA,IAAI,KAAK,MAAM,EACf;IACA,IAAIhC,IAAI,CAACoC,SAAS,EAAE;MAClB,IAAI,CAACzB,SAAK,GAAI,CAAC;IACjB;EACF;EAEA,IAAIX,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAAC1B,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAAC8B,GAAG,CAAC;IACf,IAAI,CAACtB,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACR,KAAK,CAAC8B,GAAG,CAAC;EACjB;EAEA,IAEEjC,IAAI,CAAC6B,QAAQ,EACb;IAEA,IAAI,CAAClB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACZ,OAAO,CACVC,IAAI,EACJA,IAAI,CAACqC,QAAQ,IAAIrC,IAAI,CAACiC,GAAG,CAAClB,IAAI,KAAK,eAAe,GAAGuB,SAAS,GAAGtC,IAAI,CAACiC,GAAG,EACzEK,SACF,CAAC;AACH;AAEO,SAASC,UAAUA,CAExBvC,IAG6B,EAC7BwC,qBAA+B,EAC/B;EACA,IAAIxC,IAAI,CAACyC,SAAS,EAAE;IAClB,IAAI,CAACzC,IAAI,CAACgB,UAAU,EAAE;MACpB,IAAI,CAACL,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACc,KAAK,CAAC,CAAC;IACZ,IAAI,CAACtB,KAAK,CAACH,IAAI,CAACyC,SAAS,EAAED,qBAAqB,CAAC;EACnD;AACF;AAEO,SAASE,aAAaA,CAE3B1C,IAAwE,EACxE2C,MAA8B,EAC9B;EACA,IAAI3C,IAAI,CAACmC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;IAIlB,IAAI,CAACU,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACnB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACS,IAAI,CAAC,UAAU,CAAC;EACrB,IAAIlC,IAAI,CAACoC,SAAS,EAAE;IAIlB,IAAI,CAACQ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACjC,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACc,KAAK,CAAC,CAAC;EACZ,IAAIzB,IAAI,CAAC6C,EAAE,EAAE;IACX,IAAI,CAAC1C,KAAK,CAACH,IAAI,CAAC6C,EAAE,CAAC;EACrB;EAEA,IAAI,CAAC9C,OAAO,CAACC,IAAI,EAAEA,IAAI,CAAC6C,EAAE,EAAEF,MAAM,CAAC;EACnC,IAAI3C,IAAI,CAACe,IAAI,KAAK,mBAAmB,EAAE;IACrC,IAAI,CAACwB,UAAU,CAACvC,IAAI,CAAC;EACvB;AACF;AAEO,SAAS8C,kBAAkBA,CAEhC9C,IAA0B,EAC1B2C,MAA8B,EAC9B;EACA,IAAI,CAACD,aAAa,CAAC1C,IAAI,EAAE2C,MAAM,CAAC;EAChC,IAAI,CAAClB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACtB,KAAK,CAACH,IAAI,CAAC+C,IAAI,CAAC;AACvB;AAIO,SAASC,uBAAuBA,CAErChD,IAA+B,EAC/B2C,MAA8B,EAC9B;EACA,IAAI3C,IAAI,CAACmC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAIA,IAAIwB,UAAU;EACd,IACE,CAAC,IAAI,CAACC,MAAM,CAACC,WAAW,IACxBnD,IAAI,CAACa,MAAM,CAACS,MAAM,KAAK,CAAC,IACxBxB,YAAY,CAAEmD,UAAU,GAAGjD,IAAI,CAACa,MAAM,CAAC,CAAC,CAAE,CAAC,IAC3C,CAACuC,kBAAkB,CAACpD,IAAI,EAAEiD,UAAU,CAAC,EACrC;IACA,IAAI,CAAC9C,KAAK,CAAC8C,UAAU,EAAE,IAAI,CAAC;EAC9B,CAAC,MAAM;IACL,IAAI,CAAClD,OAAO,CAACC,IAAI,EAAEsC,SAAS,EAAEK,MAAM,CAAC;EACvC;EAEA,IAAI,CAACJ,UAAU,CAACvC,IAAI,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACyB,KAAK,CAAC,CAAC;EAIZ,IAAI,CAAC4B,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAAC1C,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAI,CAACc,KAAK,CAAC,CAAC;EAEZ,IAAI,CAAC6B,YAAY,IAAIC,mBAAY,CAACC,SAAS;EAC3C,IAAI,CAACrD,KAAK,CAACH,IAAI,CAAC+C,IAAI,CAAC;AACvB;AAEA,SAASK,kBAAkBA,CACzBpD,IAA+B,EAC/ByD,KAAmB,EACV;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACT,OAAO,CAAC,EACN3D,IAAI,CAACI,cAAc,IACnBJ,IAAI,CAACgB,UAAU,IACfhB,IAAI,CAACyC,SAAS,IACdgB,KAAK,CAAC3B,cAAc,IACpB2B,KAAK,CAAC5B,QAAQ,KAAA6B,qBAAA,GAEdD,KAAK,CAACG,eAAe,aAArBF,qBAAA,CAAuBpC,MAAM,KAAAqC,qBAAA,GAC7BF,KAAK,CAACI,gBAAgB,aAAtBF,qBAAA,CAAwBrC,MAAM,CAC/B;AACH;AAEA,SAAShB,cAAcA,CAErBL,MAAoC,EACpC0C,MAAuE,EACvE;EACA,IAAIE,EAAyC,GAAG5C,MAAM;EAEtD,IAAI,CAAC4C,EAAE,IAAIF,MAAM,EAAE;IACjB,MAAMmB,UAAU,GAAGnB,MAAM,CAAC5B,IAAI;IAE9B,IAAI+C,UAAU,KAAK,oBAAoB,EAAE;MACvCjB,EAAE,GAAGF,MAAM,CAACE,EAAE;IAChB,CAAC,MAAM,IACLiB,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,mBAAmB,EAClC;MACAjB,EAAE,GAAGF,MAAM,CAACoB,IAAI;IAClB,CAAC,MAAM,IACLD,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,eAAe,EAC9B;MACA,IAAI,CAACnB,MAAM,CAACN,QAAQ,IAAIM,MAAM,CAACV,GAAG,CAAClB,IAAI,KAAK,eAAe,EAAE;QAC3D8B,EAAE,GAAGF,MAAM,CAACV,GAAG;MACjB;IACF,CAAC,MAAM,IACL6B,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,uBAAuB,EACtC;MACAjB,EAAE,GAAGF,MAAM,CAACV,GAAG;IACjB;EACF;EAEA,IAAI,CAACY,EAAE,EAAE;EAET,IAAIxC,QAAQ;EAEZ,IAAIwC,EAAE,CAAC9B,IAAI,KAAK,YAAY,EAAE;IAAA,IAAAiD,OAAA,EAAAC,QAAA;IAC5B5D,QAAQ,GAAG;MACTK,GAAG,GAAAsD,OAAA,GAAEnB,EAAE,CAACqB,GAAG,qBAANF,OAAA,CAAQG,KAAK;MAClB1D,IAAI,EAAE,EAAAwD,QAAA,GAAApB,EAAE,CAACqB,GAAG,qBAAND,QAAA,CAAQG,cAAc,KAAIvB,EAAE,CAACpC;IACrC,CAAC;EACH,CAAC,MAAM,IAAIoC,EAAE,CAAC9B,IAAI,KAAK,aAAa,EAAE;IAAA,IAAAsD,QAAA;IACpChE,QAAQ,GAAG;MACTK,GAAG,GAAA2D,QAAA,GAAExB,EAAE,CAACqB,GAAG,qBAANG,QAAA,CAAQF,KAAK;MAClB1D,IAAI,EAAE,GAAG,GAAGoC,EAAE,CAACA,EAAE,CAACpC;IACpB,CAAC;EACH,CAAC,MAAM,IAAIoC,EAAE,CAAC9B,IAAI,KAAK,eAAe,EAAE;IAAA,IAAAuD,QAAA;IACtCjE,QAAQ,GAAG;MACTK,GAAG,GAAA4D,QAAA,GAAEzB,EAAE,CAACqB,GAAG,qBAANI,QAAA,CAAQH,KAAK;MAClB1D,IAAI,EAAEoC,EAAE,CAAC0B;IACX,CAAC;EACH;EAEA,OAAOlE,QAAQ;AACjB", "ignoreList": []}