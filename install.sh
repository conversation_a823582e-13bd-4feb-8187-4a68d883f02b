#!/usr/bin/env bash
# Timelapse Installation Script
# Usage: sudo ./install.sh [--update]

set -euo pipefail
APP_DIR=/opt/timelapse
RUNTIME_DIR=/var/lib/timelapse
LOG_DIR=/var/log/timelapse
USER=pi
TEMP_BACKUP_DIR=/tmp/timelapse-install-backup-$$

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if user exists
if ! id "$USER" &>/dev/null; then
    log_error "User $USER does not exist. Please create it first or change USER variable."
    exit 1
fi

# Function to check essential dependencies
check_dependencies() {
    local missing_deps=()

    # Check essential dependencies
    for cmd in node npm rsync; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_error "Install with: sudo apt-get install nodejs npm rsync"
        exit 1
    fi
}

# Function to preserve existing data during installation
preserve_data() {
    log_info "Checking for existing data to preserve..."

    # Create temporary backup directory
    mkdir -p "$TEMP_BACKUP_DIR"

    # Preserve runtime data if it exists
    if [[ -d "$RUNTIME_DIR" ]]; then
        log_info "Preserving runtime data..."
        cp -r "$RUNTIME_DIR" "$TEMP_BACKUP_DIR/" 2>/dev/null || true
    fi

    # Preserve logs if they exist
    if [[ -d "$LOG_DIR" ]]; then
        log_info "Preserving log data..."
        cp -r "$LOG_DIR" "$TEMP_BACKUP_DIR/" 2>/dev/null || true
    fi

    # Preserve any config files from the old installation
    if [[ -d "$APP_DIR/backend/src/config" ]]; then
        log_info "Preserving configuration..."
        mkdir -p "$TEMP_BACKUP_DIR/config"
        cp -r "$APP_DIR/backend/src/config"/* "$TEMP_BACKUP_DIR/config/" 2>/dev/null || true
    fi
}

# Function to restore preserved data
restore_preserved_data() {
    local restore_failed=false

    log_info "Restoring preserved data..."

    # Try to restore runtime data
    if [[ -d "$TEMP_BACKUP_DIR/timelapse" ]]; then
        if cp -r "$TEMP_BACKUP_DIR/timelapse"/* "$RUNTIME_DIR/" 2>/dev/null; then
            log_success "Runtime data restored"
        else
            log_warning "Could not restore runtime data to $RUNTIME_DIR"
            restore_failed=true
        fi
    fi

    # Try to restore logs
    if [[ -d "$TEMP_BACKUP_DIR/timelapse" ]]; then
        if [[ -d "$TEMP_BACKUP_DIR/$(basename "$LOG_DIR")" ]]; then
            if cp -r "$TEMP_BACKUP_DIR/$(basename "$LOG_DIR")"/* "$LOG_DIR/" 2>/dev/null; then
                log_success "Log data restored"
            else
                log_warning "Could not restore log data to $LOG_DIR"
                restore_failed=true
            fi
        fi
    fi

    # Try to restore config
    if [[ -d "$TEMP_BACKUP_DIR/config" ]]; then
        mkdir -p "$APP_DIR/backend/src/config"
        if cp -r "$TEMP_BACKUP_DIR/config"/* "$APP_DIR/backend/src/config/" 2>/dev/null; then
            log_success "Configuration restored"
        else
            log_warning "Could not restore configuration"
        fi
    fi

    # If restore failed, keep data in safe location
    if [[ "$restore_failed" == true ]]; then
        local safe_backup="/tmp/timelapse-data-$(date +%Y%m%d-%H%M%S)"
        mv "$TEMP_BACKUP_DIR" "$safe_backup"
        log_warning "Some data could not be restored due to path changes"
        log_warning "Your data is safely stored in: $safe_backup"
        log_warning "You can manually restore it or use ./timelapse-manage.sh for advanced restore options"
    else
        # Clean up temporary backup
        rm -rf "$TEMP_BACKUP_DIR" 2>/dev/null || true
    fi
}

# Handle command line arguments
case "${1-}" in
    --update)
        log_info "Updating existing installation..."
        ;;
    "")
        log_info "Installing timelapse system..."
        ;;
    *)
        echo "Usage: $0 [--update]"
        echo ""
        echo "Options:"
        echo "  (no args)    Fresh installation"
        echo "  --update     Update existing installation"
        exit 1
        ;;
esac

# Check dependencies
check_dependencies

# Preserve existing data automatically (unless this is just an update)
if [[ "${1-}" != "--update" ]]; then
    preserve_data
fi

# 1. place / update code ----------------------------------------------------
if [[ "${1-}" != "--update" ]]; then
  rm -rf "$APP_DIR"
  mkdir -p "$APP_DIR"
  cp -r . "$APP_DIR"
else
  rsync -a --delete ./ "$APP_DIR/"
fi

# Fix ownership of the application directory
chown -R "$USER:$USER" "$APP_DIR"

# 2. build on the Pi (smart detection of what needs building) --------------

# Function to check if backend needs rebuilding
backend_needs_build() {
    local backend_changed=false

    # Check if package.json changed
    if [[ "${1-}" != "--update" ]] || ! cmp -s "backend/package.json" "$APP_DIR/backend/package.json" 2>/dev/null; then
        backend_changed=true
    fi

    # Check if node_modules exists
    if [[ ! -d "$APP_DIR/backend/node_modules" ]]; then
        backend_changed=true
    fi

    echo $backend_changed
}

# Function to check if frontend needs rebuilding
frontend_needs_build() {
    local frontend_changed=false

    # Check if package.json changed
    if [[ "${1-}" != "--update" ]] || ! cmp -s "frontend/package.json" "$APP_DIR/frontend/package.json" 2>/dev/null; then
        frontend_changed=true
    fi

    # Check if node_modules exists
    if [[ ! -d "$APP_DIR/frontend/node_modules" ]]; then
        frontend_changed=true
    fi

    # Check if dist directory exists
    if [[ ! -d "$APP_DIR/frontend/dist" ]]; then
        frontend_changed=true
    fi

    # For updates, check if any frontend source files changed
    if [[ "${1-}" == "--update" ]]; then
        # Check if any .ts, .tsx, .js, .jsx, .vue, .css files changed in src/
        if [[ -d "frontend/src" ]] && [[ -d "$APP_DIR/frontend/src" ]]; then
            if ! diff -r frontend/src "$APP_DIR/frontend/src" >/dev/null 2>&1; then
                frontend_changed=true
            fi
        fi

        # Check if vite.config.ts or other config files changed
        for config_file in "vite.config.ts" "tsconfig.json" "tailwind.config.js" "postcss.config.js"; do
            if [[ -f "frontend/$config_file" ]] && ! cmp -s "frontend/$config_file" "$APP_DIR/frontend/$config_file" 2>/dev/null; then
                frontend_changed=true
                break
            fi
        done
    fi

    echo $frontend_changed
}

# Build backend if needed
if [[ "$(backend_needs_build "${1-}")" == "true" ]]; then
    log_info "Backend changes detected, rebuilding..."
    pushd "$APP_DIR/backend"
    sudo -u "$USER" npm ci
    popd
else
    log_info "No backend changes detected, skipping npm ci"
fi

# Build frontend if needed
if [[ "$(frontend_needs_build "${1-}")" == "true" ]]; then
    log_info "Frontend changes detected, rebuilding..."
    pushd "$APP_DIR/frontend"
    sudo -u "$USER" npm ci --legacy-peer-deps
    sudo -u "$USER" npm run build --legacy-peer-deps
    popd
else
    log_info "No frontend changes detected, skipping build"
fi

# 3. runtime & log dirs -----------------------------------------------------
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR"/{images,zips,public}
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR/public/images"
install -d -o $USER -g $USER -m 755 "$LOG_DIR"

# 3.5. restore preserved data -----------------------------------------------
if [[ "${1-}" != "--update" ]] && [[ -d "$TEMP_BACKUP_DIR" ]]; then
    restore_preserved_data
fi

# 4. install / update systemd unit ------------------------------------------
sudo systemctl disable --now timelapse-backend.service || true

install -m 644 "$APP_DIR/timelapse-backend.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable timelapse-backend.service
systemctl restart timelapse-backend.service

# Wait a moment and check status
sleep 2
if systemctl is-active --quiet timelapse-backend.service; then
    log_success "Timelapse installed / updated successfully!"
    echo ""
    echo "📊 Service status: $(systemctl is-active timelapse-backend.service)"
    echo "🔗 Backend running on: http://localhost:4077"
    echo "💾 Data location: $RUNTIME_DIR"
    echo "📝 Log location: $LOG_DIR"
    echo ""
    echo "Management commands:"
    echo "  ./timelapse-manage.sh status    - Show status"
    echo "  ./timelapse-manage.sh logs      - View logs"
    echo "  ./timelapse-manage.sh backup    - Create backup"
    echo "  ./timelapse-manage.sh           - Show all options"
else
    log_error "Service failed to start. Check logs: journalctl -u timelapse-backend -n 20"
    exit 1
fi
