#!/usr/bin/env bash
# Timelapse Installation Script
# Usage: sudo ./install.sh [--update|--backup|--restore <backup-file>]

set -euo pipefail
APP_DIR=/opt/timelapse
RUNTIME_DIR=/var/lib/timelapse
LOG_DIR=/var/log/timelapse
USER=pi
BACKUP_DIR=/tmp/timelapse-backups

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if user exists
if ! id "$USER" &>/dev/null; then
    log_error "User $USER does not exist. Please create it first or change USER variable."
    exit 1
fi

# Function to check system dependencies
check_dependencies() {
    log_info "Checking system dependencies..."

    local missing_deps=()
    local optional_deps=()

    # Required dependencies
    local required_deps=(
        "node:Node.js (install with: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs)"
        "npm:NPM (usually comes with Node.js)"
        "rsync:rsync (install with: sudo apt-get install rsync)"
        "systemctl:systemd (should be available on modern systems)"
    )

    # Optional but recommended dependencies
    local optional_deps_list=(
        "gphoto2:Camera control (install with: sudo apt-get install gphoto2)"
        "pigpio:GPIO control (install with: sudo apt-get install pigpio)"
        "zip:Archive creation (install with: sudo apt-get install zip)"
    )

    # Check required dependencies
    for dep_info in "${required_deps[@]}"; do
        local cmd="${dep_info%%:*}"
        local desc="${dep_info#*:}"
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$desc")
        fi
    done

    # Check optional dependencies
    for dep_info in "${optional_deps_list[@]}"; do
        local cmd="${dep_info%%:*}"
        local desc="${dep_info#*:}"
        if ! command -v "$cmd" &> /dev/null; then
            optional_deps+=("$desc")
        fi
    done

    # Report missing required dependencies
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "Missing required dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        exit 1
    fi

    # Report missing optional dependencies
    if [[ ${#optional_deps[@]} -gt 0 ]]; then
        log_warning "Missing optional dependencies (timelapse may have limited functionality):"
        for dep in "${optional_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi

    log_success "All required dependencies are available"
}

# Function to create backup
create_backup() {
    local backup_name="timelapse-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    local backup_path="$BACKUP_DIR/$backup_name"

    log_info "Creating backup..."

    # Create backup directory
    mkdir -p "$BACKUP_DIR"

    # Check what exists to backup
    local backup_items=()
    [[ -d "$RUNTIME_DIR" ]] && backup_items+=("$RUNTIME_DIR")
    [[ -d "$LOG_DIR" ]] && backup_items+=("$LOG_DIR")
    [[ -d "$APP_DIR/backend/src/config" ]] && backup_items+=("$APP_DIR/backend/src/config")

    if [[ ${#backup_items[@]} -eq 0 ]]; then
        log_warning "No existing data found to backup"
        return 0
    fi

    # Create backup
    tar -czf "$backup_path" "${backup_items[@]}" 2>/dev/null || {
        log_warning "Some files couldn't be backed up (this is normal for first install)"
    }

    if [[ -f "$backup_path" ]]; then
        log_success "Backup created: $backup_path"
        echo "Backup contains:"
        tar -tzf "$backup_path" 2>/dev/null | head -10
        [[ $(tar -tzf "$backup_path" 2>/dev/null | wc -l) -gt 10 ]] && echo "  ... and more"
        echo ""
        return 0
    else
        log_warning "No backup created (no existing data found)"
        return 0
    fi
}

# Function to restore from backup
restore_backup() {
    local backup_file="$1"

    if [[ ! -f "$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi

    log_info "Restoring from backup: $backup_file"

    # Show what will be restored
    echo "Backup contains:"
    tar -tzf "$backup_file" | head -10
    [[ $(tar -tzf "$backup_file" | wc -l) -gt 10 ]] && echo "  ... and more"
    echo ""

    read -p "Restore this backup? This will overwrite existing data. (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled"
        exit 0
    fi

    # Stop service before restore
    systemctl stop timelapse-backend.service 2>/dev/null || true

    # Restore backup
    tar -xzf "$backup_file" -C / 2>/dev/null || {
        log_error "Failed to restore backup"
        exit 1
    }

    # Fix permissions after restore
    [[ -d "$RUNTIME_DIR" ]] && chown -R "$USER:$USER" "$RUNTIME_DIR"
    [[ -d "$LOG_DIR" ]] && chown -R "$USER:$USER" "$LOG_DIR"

    log_success "Backup restored successfully"
}

# Function to check for existing data and warn user
check_existing_data() {
    local has_data=false
    local data_info=()

    # Check for images
    if [[ -d "$RUNTIME_DIR/images" ]] && [[ $(find "$RUNTIME_DIR/images" -name "*.jpg" 2>/dev/null | wc -l) -gt 0 ]]; then
        local img_count=$(find "$RUNTIME_DIR/images" -name "*.jpg" 2>/dev/null | wc -l)
        data_info+=("📸 $img_count captured images")
        has_data=true
    fi

    # Check for zips
    if [[ -d "$RUNTIME_DIR/zips" ]] && [[ $(find "$RUNTIME_DIR/zips" -name "*.zip" 2>/dev/null | wc -l) -gt 0 ]]; then
        local zip_count=$(find "$RUNTIME_DIR/zips" -name "*.zip" 2>/dev/null | wc -l)
        data_info+=("📦 $zip_count zip archives")
        has_data=true
    fi

    # Check for logs
    if [[ -d "$LOG_DIR" ]] && [[ $(find "$LOG_DIR" -name "*.log" 2>/dev/null | wc -l) -gt 0 ]]; then
        data_info+=("📋 Log files")
        has_data=true
    fi

    if [[ "$has_data" == true ]]; then
        log_warning "Existing timelapse data found:"
        for info in "${data_info[@]}"; do
            echo "  $info"
        done
        echo ""
        echo "Options:"
        echo "  1. Create backup and continue (recommended)"
        echo "  2. Continue without backup (data will be lost)"
        echo "  3. Cancel installation"
        echo ""
        read -p "Choose option (1/2/3): " -n 1 -r
        echo ""

        case $REPLY in
            1)
                create_backup
                ;;
            2)
                log_warning "Continuing without backup - existing data may be lost"
                ;;
            3)
                log_info "Installation cancelled"
                exit 0
                ;;
            *)
                log_error "Invalid option"
                exit 1
                ;;
        esac
    fi
}

# Handle command line arguments
case "${1-}" in
    --backup)
        log_info "Creating backup only..."
        create_backup
        exit 0
        ;;
    --list-backups)
        log_info "Available backups in $BACKUP_DIR:"
        if [[ -d "$BACKUP_DIR" ]] && [[ $(find "$BACKUP_DIR" -name "*.tar.gz" 2>/dev/null | wc -l) -gt 0 ]]; then
            find "$BACKUP_DIR" -name "*.tar.gz" -printf "%T@ %Tc %p\n" | sort -n | cut -d' ' -f2-
        else
            echo "No backups found"
        fi
        exit 0
        ;;
    --restore)
        if [[ -z "${2-}" ]]; then
            log_error "Please specify backup file: $0 --restore <backup-file>"
            echo "Use '$0 --list-backups' to see available backups"
            exit 1
        fi
        restore_backup "$2"
        exit 0
        ;;
    --update)
        log_info "Updating existing installation..."
        ;;
    "")
        log_info "Installing timelapse system..."
        ;;
    *)
        echo "Usage: $0 [--update|--backup|--restore <backup-file>|--list-backups]"
        echo ""
        echo "Options:"
        echo "  (no args)              Fresh installation"
        echo "  --update              Update existing installation"
        echo "  --backup              Create backup only"
        echo "  --list-backups        List available backups"
        echo "  --restore <file>      Restore from backup"
        exit 1
        ;;
esac

# Check dependencies
check_dependencies

# Check for existing data (unless this is just an update)
if [[ "${1-}" != "--update" ]]; then
    check_existing_data
fi

# 1. place / update code ----------------------------------------------------
if [[ "${1-}" != "--update" ]]; then
  rm -rf "$APP_DIR"
  mkdir -p "$APP_DIR"
  cp -r . "$APP_DIR"
else
  rsync -a --delete ./ "$APP_DIR/"
fi

# 2. build on the Pi (or skip if dist already copied) -----------------------
pushd "$APP_DIR/backend"
sudo -u "$USER" npm ci
popd

pushd "$APP_DIR/frontend"
sudo -u "$USER" npm ci
sudo -u "$USER" npm run build
popd

# 3. runtime & log dirs -----------------------------------------------------
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR"/{images,zips,public}
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR/public/images"
install -d -o $USER -g $USER -m 755 "$LOG_DIR"

# 4. install / update systemd unit ------------------------------------------
sudo systemctl disable --now timelapse-backend.service || true

install -m 644 "$APP_DIR/timelapse-backend.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable timelapse-backend.service
systemctl restart timelapse-backend.service

# Wait a moment and check status
sleep 2
if systemctl is-active --quiet timelapse-backend.service; then
    log_success "Timelapse installed / updated successfully!"
    echo ""
    echo "📊 Service status: $(systemctl is-active timelapse-backend.service)"
    echo "🔗 Backend running on: http://localhost:4077"
    echo "📋 View logs: journalctl -u timelapse-backend -f"
    echo "💾 Data location: $RUNTIME_DIR"
    echo "📝 Log location: $LOG_DIR"
    echo ""
    echo "Backup commands:"
    echo "  Create backup: sudo $0 --backup"
    echo "  Restore backup: sudo $0 --restore <backup-file>"
    echo "  Backups stored in: $BACKUP_DIR"
else
    log_error "Service failed to start. Check logs: journalctl -u timelapse-backend -n 20"
    exit 1
fi
