#!/usr/bin/env bash
# Run with: sudo ./install.sh [--update]

set -euo pipefail
APP_DIR=/opt/timelapse
RUNTIME_DIR=/var/lib/timelapse
LOG_DIR=/var/log/timelapse
USER=pi

# 1. place / update code ----------------------------------------------------
if [[ "${1-}" != "--update" ]]; then
  rm -rf "$APP_DIR"
  mkdir -p "$APP_DIR"
  cp -r . "$APP_DIR"
else
  rsync -a --delete ./ "$APP_DIR/"
fi

# 2. build on the Pi (or skip if dist already copied) -----------------------
pushd "$APP_DIR/backend"
sudo -u "$USER" npm ci
popd

pushd "$APP_DIR/frontend"
sudo -u "$USER" npm ci
sudo -u "$USER" npm run build
popd

# 3. runtime & log dirs -----------------------------------------------------
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR"/{images,zips}
install -d -o $USER -g $USER -m 755 "$LOG_DIR"

# 4. install / update systemd unit ------------------------------------------
sudo systemctl disable --now timelapse-backend.service || true

install -m 644 "$APP_DIR/timelapse-backend.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable timelapse-backend.service
systemctl restart timelapse-backend.service

echo "🎉 Timelapse installed / updated. Logs: journalctl -u timelapse-backend -f"
