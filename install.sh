#!/usr/bin/env bash
# Run with: sudo ./install.sh [--update]

set -euo pipefail
APP_DIR=/opt/timelapse
RUNTIME_DIR=/var/lib/timelapse
LOG_DIR=/var/log/timelapse
USER=pi

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root (use sudo)"
   exit 1
fi

# Check if user exists
if ! id "$USER" &>/dev/null; then
    echo "User $USER does not exist. Please create it first or change USER variable."
    exit 1
fi

# 1. place / update code ----------------------------------------------------
if [[ "${1-}" != "--update" ]]; then
  rm -rf "$APP_DIR"
  mkdir -p "$APP_DIR"
  cp -r . "$APP_DIR"
else
  rsync -a --delete ./ "$APP_DIR/"
fi

# 2. build on the Pi (or skip if dist already copied) -----------------------
pushd "$APP_DIR/backend"
sudo -u "$USER" npm ci
popd

pushd "$APP_DIR/frontend"
sudo -u "$USER" npm ci
sudo -u "$USER" npm run build
popd

# 3. runtime & log dirs -----------------------------------------------------
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR"/{images,zips,public}
install -d -o $USER -g $USER -m 755 "$RUNTIME_DIR/public/images"
install -d -o $USER -g $USER -m 755 "$LOG_DIR"

# 4. install / update systemd unit ------------------------------------------
sudo systemctl disable --now timelapse-backend.service || true

install -m 644 "$APP_DIR/timelapse-backend.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable timelapse-backend.service
systemctl restart timelapse-backend.service

# Wait a moment and check status
sleep 2
if systemctl is-active --quiet timelapse-backend.service; then
    echo "🎉 Timelapse installed / updated successfully!"
    echo "📊 Service status: $(systemctl is-active timelapse-backend.service)"
    echo "🔗 Backend running on: http://localhost:4077"
    echo "📋 View logs: journalctl -u timelapse-backend -f"
else
    echo "❌ Service failed to start. Check logs: journalctl -u timelapse-backend -n 20"
    exit 1
fi
