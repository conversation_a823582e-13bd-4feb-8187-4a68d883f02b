# /etc/systemd/system/timelapse-backend.service
[Unit]
Description=Timelapse backend (Node + pigpio)
After=network-online.target
Wants=network-online.target

[Service]
# pigpio requires /dev/mem --> run as root
User=root
WorkingDirectory=/opt/timelapse/backend
Environment=PORT=4077
Environment=LOG_DIR=/var/log/timelapse
Environment=IMAGES_DIR=/var/lib/timelapse/images
Environment=ZIPS_DIR=/var/lib/timelapse/zips
Environment=PUBLIC_DIR=/var/lib/timelapse/public
Environment=NODE_ENV=production

# start command
ExecStart=/usr/bin/npm start

# Hardening (relaxed for camera access)
PrivateTmp=true
ProtectSystem=full
ReadWritePaths=/var/lib/timelapse /var/log/timelapse
CapabilityBoundingSet=CAP_SYS_RAWIO
NoNewPrivileges=true

# Reliability
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target