# /etc/systemd/system/timelapse-backend.service
[Unit]
Description=Timelapse backend (Node + pigpio)
After=network-online.target
Wants=network-online.target

[Service]
# pigpio requires /dev/mem --> run as root
User=root
WorkingDirectory=/opt/timelapse/backend
Environment=PORT=4077

# start command
ExecStart=/usr/bin/npm start

# Hardening
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
CapabilityBoundingSet=CAP_SYS_RAWIO
NoNewPrivileges=true

# Reliability
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target