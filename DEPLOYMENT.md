# Timelapse Deployment Guide

## Quick Start

```bash
# Fresh installation (automatically preserves existing data)
sudo ./install.sh

# Update existing installation (updates code only, data stays in place)
sudo ./install.sh --update
```

## Advanced Management

```bash
# Create manual backup
./timelapse-manage.sh backup

# List available backups
./timelapse-manage.sh list-backups

# Restore from backup
./timelapse-manage.sh restore /tmp/timelapse-backups/backup-file.tar.gz

# Show service status
./timelapse-manage.sh status
```

## System Requirements

### Required Dependencies
- **Node.js 18+** - `curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs`
- **NPM** - Usually comes with Node.js
- **rsync** - `sudo apt-get install rsync`
- **systemd** - Available on modern Linux systems

### Optional Dependencies (for full functionality)
- **gphoto2** - Camera control: `sudo apt-get install gphoto2`
- **pigpio** - GPIO control: `sudo apt-get install pigpio`
- **zip** - Archive creation: `sudo apt-get install zip`

## Installation Options

### Fresh Installation
```bash
sudo ./install.sh
```
- Checks essential dependencies (node, npm, rsync)
- **Automatically preserves existing data** during installation
- Installs application to `/opt/timelapse`
- Creates runtime directories in `/var/lib/timelapse`
- Restores preserved data to new locations
- Sets up systemd service

### Update Existing Installation
```bash
sudo ./install.sh --update
```
- Updates code using rsync (efficient, preserves data)
- No data preservation needed (data stays in place)
- Restarts service with new code

### Data Preservation Logic
- **Fresh install**: Automatically backs up data before removing `/opt/timelapse`, then restores it
- **Update**: Uses rsync to update code in place, no data movement needed
- **Fallback**: If restoration fails due to path changes, data is kept in `/tmp/timelapse-data-TIMESTAMP`

## Data Management

### Directory Structure
```
/opt/timelapse/                    # Application code
├── backend/
├── frontend/
└── timelapse-backend.service

/var/lib/timelapse/               # Runtime data (preserved during updates)
├── images/                       # Captured images
├── zips/                         # Zip archives
└── public/
    └── images/                   # first.jpg, last.jpg

/var/log/timelapse/               # Logs
└── timelapse.log

/tmp/timelapse-backups/           # Backups
└── timelapse-backup-*.tar.gz
```

### Backup & Restore

#### Create Backup
```bash
sudo ./install.sh --backup
```
Creates timestamped backup in `/tmp/timelapse-backups/`

#### List Available Backups
```bash
sudo ./install.sh --list-backups
```

#### Restore from Backup
```bash
sudo ./install.sh --restore /tmp/timelapse-backups/timelapse-backup-20241201-143022.tar.gz
```

**Note**: Restoring will:
- ✅ Restore all captured images and zip files
- ✅ Restore configuration files
- ✅ Restore log files
- ⚠️ Reset system state (counters, active sessions)

This is actually beneficial as it gives you a fresh start with your existing data.

## Service Management

### Using the Management Script
```bash
# Show status
./timelapse-manage.sh status

# View logs
./timelapse-manage.sh logs

# Follow logs in real-time
./timelapse-manage.sh follow

# Restart service
./timelapse-manage.sh restart

# Show disk usage
./timelapse-manage.sh disk

# Create backup
./timelapse-manage.sh backup
```

### Manual Service Commands
```bash
# Check status
sudo systemctl status timelapse-backend

# Start/stop/restart
sudo systemctl start timelapse-backend
sudo systemctl stop timelapse-backend
sudo systemctl restart timelapse-backend

# View logs
journalctl -u timelapse-backend -f
```

## Configuration

### Environment Variables (in systemd service)
- `PORT=4077` - Backend server port
- `LOG_DIR=/var/log/timelapse` - Log directory
- `IMAGES_DIR=/var/lib/timelapse/images` - Captured images
- `ZIPS_DIR=/var/lib/timelapse/zips` - Zip archives
- `PUBLIC_DIR=/var/lib/timelapse/public` - Static files
- `NODE_ENV=production` - Production mode

### Customization
Edit `timelapse-backend.service` before installation to change:
- Port number
- Directory locations
- User account
- Environment variables

## Troubleshooting

### Service Won't Start
```bash
# Check detailed logs
journalctl -u timelapse-backend -n 50

# Check dependencies
./install.sh --help  # Shows dependency check
```

### Permission Issues
```bash
# Fix ownership
sudo chown -R pi:pi /var/lib/timelapse
sudo chown -R pi:pi /var/log/timelapse
```

### Camera Issues
```bash
# Test camera connection
gphoto2 --auto-detect

# Kill conflicting processes
sudo pkill gvfsd-gphoto2
```

### GPIO Issues
- Ensure running as root (service does this automatically)
- Check if pigpio daemon is running: `sudo systemctl status pigpiod`

## Security Notes

- Service runs as root (required for GPIO access)
- Systemd hardening applied where possible
- Data directories owned by `pi` user
- Application code in `/opt/timelapse` (standard location)

## Backup Strategy

1. **Automatic**: Install script offers backup before major changes
2. **Manual**: Use `sudo ./install.sh --backup` regularly
3. **External**: Copy `/tmp/timelapse-backups/` to external storage
4. **Scheduled**: Add to cron for regular backups

Example cron entry (weekly backup):
```bash
0 2 * * 0 cd /opt/timelapse && ./install.sh --backup
```
