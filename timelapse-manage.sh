#!/usr/bin/env bash
# Timelapse Management Helper Script
# Usage: ./timelapse-manage.sh [command]

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

SERVICE_NAME="timelapse-backend.service"
RUNTIME_DIR="/var/lib/timelapse"
LOG_DIR="/var/log/timelapse"
BACKUP_DIR="/tmp/timelapse-backups"

# Function to show service status
show_status() {
    log_info "Timelapse Service Status"
    echo "========================"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "Service is running"
        echo "Port: $(systemctl show "$SERVICE_NAME" --property=Environment | grep -o 'PORT=[0-9]*' | cut -d= -f2 || echo '4077')"
        echo "Uptime: $(systemctl show "$SERVICE_NAME" --property=ActiveEnterTimestamp | cut -d= -f2)"
    else
        log_error "Service is not running"
    fi
    
    echo ""
    echo "Data locations:"
    echo "  Images: $RUNTIME_DIR/images ($(find "$RUNTIME_DIR/images" -name "*.jpg" 2>/dev/null | wc -l) files)"
    echo "  Zips: $RUNTIME_DIR/zips ($(find "$RUNTIME_DIR/zips" -name "*.zip" 2>/dev/null | wc -l) files)"
    echo "  Logs: $LOG_DIR"
}

# Function to show logs
show_logs() {
    local lines="${1:-50}"
    log_info "Showing last $lines log entries"
    journalctl -u "$SERVICE_NAME" -n "$lines" --no-pager
}

# Function to follow logs
follow_logs() {
    log_info "Following logs (Ctrl+C to stop)"
    journalctl -u "$SERVICE_NAME" -f
}

# Function to restart service
restart_service() {
    log_info "Restarting timelapse service..."
    sudo systemctl restart "$SERVICE_NAME"
    sleep 2
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "Service restarted successfully"
    else
        log_error "Service failed to start"
        journalctl -u "$SERVICE_NAME" -n 10 --no-pager
    fi
}

# Function to show disk usage
show_disk_usage() {
    log_info "Disk Usage"
    echo "==========="
    
    if [[ -d "$RUNTIME_DIR" ]]; then
        echo "Runtime directory: $(du -sh "$RUNTIME_DIR" 2>/dev/null | cut -f1)"
        echo "  Images: $(du -sh "$RUNTIME_DIR/images" 2>/dev/null | cut -f1 || echo '0B')"
        echo "  Zips: $(du -sh "$RUNTIME_DIR/zips" 2>/dev/null | cut -f1 || echo '0B')"
    fi
    
    if [[ -d "$LOG_DIR" ]]; then
        echo "Logs: $(du -sh "$LOG_DIR" 2>/dev/null | cut -f1)"
    fi
    
    echo ""
    echo "Available space:"
    df -h "$RUNTIME_DIR" 2>/dev/null || df -h /
}

# Main menu
show_menu() {
    echo "Timelapse Management"
    echo "==================="
    echo ""
    echo "Commands:"
    echo "  status     - Show service status and data info"
    echo "  logs       - Show recent logs"
    echo "  follow     - Follow logs in real-time"
    echo "  restart    - Restart the service"
    echo "  disk       - Show disk usage"
    echo "  backup     - Create backup"
    echo "  install    - Run installer"
    echo ""
    echo "Usage: $0 [command]"
}

# Handle command line arguments
case "${1-}" in
    status)
        show_status
        ;;
    logs)
        show_logs "${2:-50}"
        ;;
    follow)
        follow_logs
        ;;
    restart)
        restart_service
        ;;
    disk)
        show_disk_usage
        ;;
    backup)
        sudo ./install.sh --backup
        ;;
    install)
        sudo ./install.sh
        ;;
    *)
        show_menu
        ;;
esac
