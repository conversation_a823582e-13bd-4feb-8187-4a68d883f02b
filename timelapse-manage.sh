#!/usr/bin/env bash
# Timelapse Management Helper Script
# Usage: ./timelapse-manage.sh [command]

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

SERVICE_NAME="timelapse-backend.service"
RUNTIME_DIR="/var/lib/timelapse"
LOG_DIR="/var/log/timelapse"
APP_DIR="/opt/timelapse"
BACKUP_DIR="/tmp/timelapse-backups"

# Function to show service status
show_status() {
    log_info "Timelapse Service Status"
    echo "========================"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "Service is running"
        echo "Port: $(systemctl show "$SERVICE_NAME" --property=Environment | grep -o 'PORT=[0-9]*' | cut -d= -f2 || echo '4077')"
        echo "Uptime: $(systemctl show "$SERVICE_NAME" --property=ActiveEnterTimestamp | cut -d= -f2)"
    else
        log_error "Service is not running"
    fi
    
    echo ""
    echo "Data locations:"
    echo "  Images: $RUNTIME_DIR/images ($(find "$RUNTIME_DIR/images" -name "*.jpg" 2>/dev/null | wc -l) files)"
    echo "  Zips: $RUNTIME_DIR/zips ($(find "$RUNTIME_DIR/zips" -name "*.zip" 2>/dev/null | wc -l) files)"
    echo "  Logs: $LOG_DIR"
}

# Function to show logs
show_logs() {
    local lines="${1:-50}"
    log_info "Showing last $lines log entries"
    journalctl -u "$SERVICE_NAME" -n "$lines" --no-pager
}

# Function to follow logs
follow_logs() {
    log_info "Following logs (Ctrl+C to stop)"
    journalctl -u "$SERVICE_NAME" -f
}

# Function to restart service
restart_service() {
    log_info "Restarting timelapse service..."
    sudo systemctl restart "$SERVICE_NAME"
    sleep 2
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "Service restarted successfully"
    else
        log_error "Service failed to start"
        journalctl -u "$SERVICE_NAME" -n 10 --no-pager
    fi
}

# Function to show disk usage
show_disk_usage() {
    log_info "Disk Usage"
    echo "==========="

    if [[ -d "$RUNTIME_DIR" ]]; then
        echo "Runtime directory: $(du -sh "$RUNTIME_DIR" 2>/dev/null | cut -f1)"
        echo "  Images: $(du -sh "$RUNTIME_DIR/images" 2>/dev/null | cut -f1 || echo '0B')"
        echo "  Zips: $(du -sh "$RUNTIME_DIR/zips" 2>/dev/null | cut -f1 || echo '0B')"
    fi

    if [[ -d "$LOG_DIR" ]]; then
        echo "Logs: $(du -sh "$LOG_DIR" 2>/dev/null | cut -f1)"
    fi

    echo ""
    echo "Available space:"
    df -h "$RUNTIME_DIR" 2>/dev/null || df -h /
}

# Function to create backup
create_backup() {
    local backup_name="timelapse-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    local backup_path="$BACKUP_DIR/$backup_name"

    log_info "Creating backup..."

    # Create backup directory
    mkdir -p "$BACKUP_DIR"

    # Check what exists to backup
    local backup_items=()
    [[ -d "$RUNTIME_DIR" ]] && backup_items+=("$RUNTIME_DIR")
    [[ -d "$LOG_DIR" ]] && backup_items+=("$LOG_DIR")
    [[ -d "$APP_DIR/backend/src/config" ]] && backup_items+=("$APP_DIR/backend/src/config")

    if [[ ${#backup_items[@]} -eq 0 ]]; then
        log_warning "No existing data found to backup"
        return 0
    fi

    # Create backup
    if tar -czf "$backup_path" "${backup_items[@]}" 2>/dev/null; then
        log_success "Backup created: $backup_path"
        echo "Backup contains:"
        tar -tzf "$backup_path" 2>/dev/null | head -10
        [[ $(tar -tzf "$backup_path" 2>/dev/null | wc -l) -gt 10 ]] && echo "  ... and more"
    else
        log_error "Failed to create backup"
        return 1
    fi
}

# Function to list backups
list_backups() {
    log_info "Available backups in $BACKUP_DIR:"
    if [[ -d "$BACKUP_DIR" ]] && [[ $(find "$BACKUP_DIR" -name "*.tar.gz" 2>/dev/null | wc -l) -gt 0 ]]; then
        find "$BACKUP_DIR" -name "*.tar.gz" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | cut -d' ' -f2- || {
            # Fallback for systems without -printf
            find "$BACKUP_DIR" -name "*.tar.gz" -exec ls -la {} \;
        }
    else
        echo "No backups found"
    fi
}

# Function to restore from backup
restore_backup() {
    local backup_file="$1"

    if [[ ! -f "$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        return 1
    fi

    log_info "Restoring from backup: $backup_file"

    # Show what will be restored
    echo "Backup contains:"
    tar -tzf "$backup_file" | head -10
    [[ $(tar -tzf "$backup_file" | wc -l) -gt 10 ]] && echo "  ... and more"
    echo ""

    read -p "Restore this backup? This will overwrite existing data. (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled"
        return 0
    fi

    # Stop service before restore
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "Stopping service..."
        sudo systemctl stop "$SERVICE_NAME"
    fi

    # Restore backup
    if tar -xzf "$backup_file" -C / 2>/dev/null; then
        log_success "Backup restored successfully"

        # Fix permissions after restore
        [[ -d "$RUNTIME_DIR" ]] && sudo chown -R pi:pi "$RUNTIME_DIR"
        [[ -d "$LOG_DIR" ]] && sudo chown -R pi:pi "$LOG_DIR"

        # Restart service
        log_info "Restarting service..."
        sudo systemctl start "$SERVICE_NAME"
    else
        log_error "Failed to restore backup"
        return 1
    fi
}

# Main menu
show_menu() {
    echo "Timelapse Management"
    echo "==================="
    echo ""
    echo "Commands:"
    echo "  status         - Show service status and data info"
    echo "  logs [lines]   - Show recent logs (default: 50 lines)"
    echo "  follow         - Follow logs in real-time"
    echo "  restart        - Restart the service"
    echo "  disk           - Show disk usage"
    echo "  backup         - Create backup"
    echo "  list-backups   - List available backups"
    echo "  restore <file> - Restore from backup file"
    echo "  install        - Run installer"
    echo ""
    echo "Usage: $0 [command] [options]"
}

# Handle command line arguments
case "${1-}" in
    status)
        show_status
        ;;
    logs)
        show_logs "${2:-50}"
        ;;
    follow)
        follow_logs
        ;;
    restart)
        restart_service
        ;;
    disk)
        show_disk_usage
        ;;
    backup)
        create_backup
        ;;
    list-backups)
        list_backups
        ;;
    restore)
        if [[ -z "${2-}" ]]; then
            log_error "Please specify backup file"
            echo "Use '$0 list-backups' to see available backups"
            exit 1
        fi
        restore_backup "$2"
        ;;
    install)
        sudo ./install.sh
        ;;
    *)
        show_menu
        ;;
esac
