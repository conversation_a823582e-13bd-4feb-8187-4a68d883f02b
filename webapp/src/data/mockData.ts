import type { LogEntry, TimelapseImage } from '../types';
import { DEFAULT_TIMESTAMP_FORMAT } from '../types';

export const mockLogs: LogEntry[] = [
  { timestamp: '2024-03-15 15:00:00', level: 'info', message: 'Timelapse started' },
  { timestamp: '2024-03-15 15:15:00', level: 'warning', message: 'Low storage space' },
];

export const mockImages: {
  first: TimelapseImage | null;
  last: TimelapseImage | null;
} = {
  first: {
    url: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba',
    timestamp: DEFAULT_TIMESTAMP_FORMAT,
  },
  last: {
    url: 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba',
    timestamp: DEFAULT_TIMESTAMP_FORMAT,
  },
};