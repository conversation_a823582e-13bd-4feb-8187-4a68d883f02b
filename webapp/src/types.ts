// Constants
export const DEFAULT_TIMESTAMP_FORMAT = '----/--/-- --:--:--';

export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
}

export interface StorageInfo {
  used: number;      // Used space in bytes
  total: number;     // Total space in bytes
  percentage: number; // Used percentage (0-100)
}

export interface ZipFile {
  name: string;
  size: number;
  created: string;
}

export interface TimelapseImage {
  url: string;
  timestamp: string;
}

export interface Settings {
  isCameraConnected: boolean;
  light1State: boolean;
  light2State: boolean;
  light3State: boolean;
  light1Connected: boolean;
  light2Connected: boolean;
  light3Connected: boolean;
  light1ControlMode?: 'direct' | 'toggle';
  light2ControlMode?: 'direct' | 'toggle';
  light3ControlMode?: 'direct' | 'toggle';
  light1PulseWidth?: number;
  light2PulseWidth?: number;
  light3PulseWidth?: number;
  light1StatusKnown?: boolean;
  light2StatusKnown?: boolean;
  light3StatusKnown?: boolean;
  light1CaptureAction?: 'none' | 'on' | 'off';
  light2CaptureAction?: 'none' | 'on' | 'off';
  light3CaptureAction?: 'none' | 'on' | 'off';
  light1StartTime?: string; // Format: 'HH:MM'
  light2StartTime?: string;
  light3StartTime?: string;
  light1EndTime?: string; // Format: 'HH:MM'
  light2EndTime?: string;
  light3EndTime?: string;
  light1Mode?: 'scheduled' | 'alwaysOn' | 'alwaysOff';
  light2Mode?: 'scheduled' | 'alwaysOn' | 'alwaysOff';
  light3Mode?: 'scheduled' | 'alwaysOn' | 'alwaysOff';
  frequency: number;
  timelapseActive: boolean;
  timelapsePaused: boolean;
  networkStatus: boolean; // Indicates if the frontend is connected to the backend server
  imagesTaken: number; // Total images taken
  timelapseImagesTaken: number; // Images taken during timelapse
  manualImagesTaken: number; // Images taken manually
  nextCapture: string;
  storage: StorageInfo; // Add storage information
  availableImages: number;
  // zipFiles: ZipFile[];
  logsExpanded: boolean;
  // DHT22 sensor data
  currentTemp?: number; // Current temperature in Celsius
  currentRH?: number; // Current relative humidity in %
  tempHistory?: number[]; // Array of temperature readings
  rhHistory?: number[]; // Array of humidity readings
}