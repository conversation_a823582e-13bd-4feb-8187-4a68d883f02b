import { TimelapseImage } from '../types';
import { DEFAULT_TIMESTAMP_FORMAT } from '../types';

// PNG placeholder images from public/assets folder
export const FIRST_IMAGE_PLACEHOLDER = '/assets/first.png';
export const LAST_IMAGE_PLACEHOLDER = '/assets/last.png';

// Default image objects for use when no real images are available
export const DEFAULT_FIRST_IMAGE: TimelapseImage = {
  url: FIRST_IMAGE_PLACEHOLDER,
  timestamp: DEFAULT_TIMESTAMP_FORMAT
};

export const DEFAULT_LAST_IMAGE: TimelapseImage = {
  url: LAST_IMAGE_PLACEHOLDER,
  timestamp: DEFAULT_TIMESTAMP_FORMAT
};