import React from 'react';
import { Video, VideoOff } from 'lucide-react';
import { useRecordingIndicator } from '../../hooks/useRecordingIndicator';

interface VideoRecordButtonProps {
  isRecording: boolean;
  onToggleRecording: () => void;
  disabled?: boolean;
  disabledReason?: string;
}

export function VideoRecordButton({
  isRecording,
  onToggleRecording,
  disabled,
  disabledReason
}: VideoRecordButtonProps) {
  const pulseClass = useRecordingIndicator(isRecording);
  
  return (
    <button
      onClick={onToggleRecording}
      disabled={disabled}
      title={disabled ? disabledReason : undefined}
      className={`
        w-full flex items-center justify-center gap-2 
        ${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'}
        disabled:bg-gray-300 dark:disabled:bg-gray-600
        text-white rounded-lg py-2 px-4 transition-colors
        disabled:cursor-not-allowed
      `}
    >
      <div className="relative">
        {isRecording ? <VideoOff size={20} /> : <Video size={20} />}
        {isRecording && (
          <div className={`absolute -top-1 -right-1 w-2 h-2 rounded-full bg-red-500 ${pulseClass}`} />
        )}
      </div>
      {isRecording ? 'Stop Recording' : 'Record Video'}
    </button>
  );
}