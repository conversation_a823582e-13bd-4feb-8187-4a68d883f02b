import React from 'react';

interface LightStatusConfirmProps {
  lightNumber: number;
  isOpen: boolean;
  onConfirm: (status: boolean) => void;
  onCancel: () => void;
}

export function LightStatusConfirm({ 
  lightNumber, 
  isOpen, 
  onConfirm, 
  onCancel 
}: LightStatusConfirmProps) {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full shadow-xl">
        <h3 className="text-lg font-semibold mb-4 dark:text-white">Confirm Light Status</h3>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          Please confirm the current status of Light {lightNumber}:
        </p>
        <div className="flex justify-center gap-4">
          <button
            onClick={() => onConfirm(false)}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Light is OFF
          </button>
          <button
            onClick={() => onConfirm(true)}
            className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
          >
            Light is ON
          </button>
        </div>
        <div className="mt-4 text-center">
          <button
            onClick={onCancel}
            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
