interface LightDurationSliderProps {
  percentage: number;
  onChange: (value: number) => void;
  disabled?: boolean;
}

export function LightDurationSlider({
  percentage,
  onChange,
  disabled
}: LightDurationSliderProps) {
  // Convert percentage to hours in a 24-hour cycle
  const onHours = Math.round((24 * percentage) / 100);
  const offHours = 24 - onHours;
  const displayValue = `${onHours} hours ON / ${offHours} hours OFF per day`;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label htmlFor="lightDuration" className="text-sm font-medium dark:text-gray-200">
          Duration
        </label>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {displayValue}
        </span>
      </div>

      <div className="relative h-2 bg-gray-200 rounded-lg dark:bg-gray-700">
        <div
          className="absolute h-full bg-green-500 rounded-lg transition-all"
          style={{ width: `${percentage}%` }}
        />
        <input
          type="range"
          id="lightDuration"
          min={0}
          max={100}
          value={percentage}
          onChange={(e) => {
            const newVal = Number(e.target.value);
            onChange(newVal);
          }}
          disabled={disabled}
          className="absolute w-full h-full opacity-0 cursor-pointer
            disabled:opacity-50 disabled:cursor-not-allowed"
        />
      </div>

      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>0 hours ON per day</span>
        <span>24 hours ON per day</span>
      </div>
    </div>
  );
}