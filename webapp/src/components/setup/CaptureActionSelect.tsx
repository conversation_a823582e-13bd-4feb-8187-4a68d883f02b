import React from 'react';

type CaptureAction = 'none' | 'on' | 'off';

interface CaptureActionSelectProps {
  value: CaptureAction;
  onChange: (action: CaptureAction) => void;
  disabled?: boolean;
  label?: string;
}

export function CaptureActionSelect({ value, onChange, disabled, label }: CaptureActionSelectProps) {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value as CaptureAction);
  };

  return (
    <div className="flex items-center gap-2">
      {label && (
        <label className="text-sm text-gray-700 dark:text-gray-300 min-w-[80px]">{label}</label>
      )}
      <select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        className="p-1 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 w-full"
      >
        <option value="none">No Action</option>
        <option value="on">Force ON</option>
        <option value="off">Force OFF</option>
      </select>
    </div>
  );
}
