import React from 'react';

export type LightMode = 'scheduled' | 'alwaysOn' | 'alwaysOff';

interface ModeSelectProps {
  value: LightMode;
  onChange: (mode: LightMode) => void;
  disabled?: boolean;
  label?: string;
  groupName: string; // Unique name for the radio button group
}

export function ModeSelect({ value, onChange, disabled, label, groupName }: ModeSelectProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value as LightMode);
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm text-gray-700 dark:text-gray-300">{label}</label>
      )}
      <div className="flex flex-wrap gap-4">
        <label className="flex items-center gap-2">
          <input
            type="radio"
            name={groupName}
            value="scheduled"
            checked={value === 'scheduled'}
            onChange={handleChange}
            disabled={disabled}
            className="text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">Scheduled</span>
        </label>

        <label className="flex items-center gap-2">
          <input
            type="radio"
            name={groupName}
            value="alwaysOn"
            checked={value === 'alwaysOn'}
            onChange={handleChange}
            disabled={disabled}
            className="text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">Always ON</span>
        </label>

        <label className="flex items-center gap-2">
          <input
            type="radio"
            name={groupName}
            value="alwaysOff"
            checked={value === 'alwaysOff'}
            onChange={handleChange}
            disabled={disabled}
            className="text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">Always OFF</span>
        </label>
      </div>
    </div>
  );
}
