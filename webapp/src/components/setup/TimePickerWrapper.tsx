import { useState } from 'react';
import ReactDOM from 'react-dom';
import Time<PERSON>eeper from 'react-timekeeper';

interface TimePickerWrapperProps {
  value: string; // Format: 'HH:MM'
  onChange: (time: string) => void;
  disabled?: boolean;
  label?: string;
}

export function TimePickerWrapper({ value, onChange, disabled, label }: TimePickerWrapperProps) {
  const [showPicker, setShowPicker] = useState(false);

  // Parse the time string to get hours and minutes
  const parseTime = (timeStr: string) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return {
      hour: hours || 0,
      minute: minutes || 0
    };
  };

  const { hour, minute } = parseTime(value);

  // Format the time for display
  const displayTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

  // Handle time change
  const handleTimeChange = (newTime: any) => {
    // Handle both object format and string format from TimeKeeper
    if (typeof newTime === 'object' && newTime !== null) {
      const hour = newTime.hour || 0;
      const minute = newTime.minute || 0;
      const formattedTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      onChange(formattedTime);
    } else if (typeof newTime === 'string') {
      // If TimeKeeper returns a string, parse it
      const [time, period] = newTime.split(' ');
      const [hourStr, minuteStr] = time.split(':');
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      // Convert to 24-hour format if needed
      if (period && period.toLowerCase() === 'pm' && hour < 12) {
        hour += 12;
      } else if (period && period.toLowerCase() === 'am' && hour === 12) {
        hour = 0;
      }

      const formattedTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      onChange(formattedTime);
    }
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2 mb-1">
        {label && (
          <label className="text-sm text-gray-700 dark:text-gray-300 min-w-[80px]">{label}</label>
        )}
        <button
          type="button"
          className={`px-3 py-1 border rounded flex items-center gap-2 ${
            disabled
              ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-500'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700'
          }`}
          onClick={() => !disabled && setShowPicker(!showPicker)}
          disabled={disabled}
        >
          <span>{displayTime}</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>

      {showPicker && ReactDOM.createPortal(
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setShowPicker(false)}
          ></div>

          {/* Time picker container */}
          <div className="relative z-50">
            <TimeKeeper
              hour24Mode
              time={{ hour, minute }}
              onChange={(data) => handleTimeChange(data)}
              onDoneClick={() => setShowPicker(false)}
              switchToMinuteOnHourSelect
              coarseMinutes={5}
            />
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
