import { formatDuration } from '../../utils/time';

interface FrequencySliderProps {
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
}

export function FrequencySlider({ value, onChange, disabled }: FrequencySliderProps) {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label htmlFor="frequency" className="text-sm font-medium dark:text-gray-200">
          Capture Frequency
        </label>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {formatDuration(value)}
        </span>
      </div>
      
      <input
        type="range"
        id="frequency"
        min="1"
        max="720"
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        disabled={disabled}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700
          disabled:opacity-50 disabled:cursor-not-allowed"
      />
      
      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>1 min</span>
        <span>12 hours</span>
      </div>
    </div>
  );
}