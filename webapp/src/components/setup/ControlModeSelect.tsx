import React, { useState } from 'react';

export type ControlMode = 'direct' | 'toggle';

interface ControlModeSelectProps {
  value: ControlMode;
  pulseWidth: number;
  onChange: (mode: ControlMode, pulseWidth?: number) => void;
  disabled?: boolean;
  label?: string;
}

export function ControlModeSelect({ 
  value, 
  pulseWidth, 
  onChange, 
  disabled, 
  label = "Control Mode:" 
}: ControlModeSelectProps) {
  const [localPulseWidth, setLocalPulseWidth] = useState(pulseWidth);
  
  const handleModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMode = e.target.value as ControlMode;
    onChange(newMode, newMode === 'toggle' ? localPulseWidth : undefined);
  };
  
  const handlePulseWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newWidth = parseInt(e.target.value);
    if (!isNaN(newWidth) && newWidth > 0) {
      setLocalPulseWidth(newWidth);
      onChange(value, newWidth);
    }
  };
  
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <label className="text-sm text-gray-700 dark:text-gray-300 min-w-[100px]">
          {label}
        </label>
        <select
          value={value}
          onChange={handleModeChange}
          disabled={disabled}
          className="px-3 py-1 border rounded bg-white text-gray-700 border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed dark:disabled:bg-gray-800 dark:disabled:text-gray-500"
        >
          <option value="direct">Direct</option>
          <option value="toggle">Toggle</option>
        </select>
      </div>
      
      {value === 'toggle' && (
        <div className="flex items-center gap-2 ml-[100px]">
          <label className="text-sm text-gray-700 dark:text-gray-300">
            Pulse width:
          </label>
          <input
            type="number"
            value={localPulseWidth}
            onChange={handlePulseWidthChange}
            disabled={disabled}
            min={1}
            max={1000}
            className="w-20 px-3 py-1 border rounded bg-white text-gray-700 border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed dark:disabled:bg-gray-800 dark:disabled:text-gray-500"
          />
          <span className="text-sm text-gray-500 dark:text-gray-400">ms</span>
        </div>
      )}
    </div>
  );
}
