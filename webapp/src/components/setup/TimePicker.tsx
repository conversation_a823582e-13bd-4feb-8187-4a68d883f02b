import { useState, useEffect } from 'react';

interface TimePickerProps {
  value: string; // Format: 'HH:MM'
  onChange: (time: string) => void;
  disabled?: boolean;
  label?: string;
}

export function TimePicker({ value, onChange, disabled, label }: TimePickerProps) {
  const [hours, setHours] = useState<number>(0);
  const [minutes, setMinutes] = useState<number>(0);

  // Parse the initial value
  useEffect(() => {
    if (value) {
      const [h, m] = value.split(':').map(Number);
      setHours(h || 0);
      setMinutes(m || 0);
    }
  }, [value]);

  // Handle hour change
  const handleHourChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newHours = parseInt(e.target.value, 10);
    setHours(newHours);
    onChange(`${newHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`);
  };

  // Handle minute change
  const handleMinuteChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMinutes = parseInt(e.target.value, 10);
    setMinutes(newMinutes);
    onChange(`${hours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`);
  };

  return (
    <div className="flex items-center gap-2">
      {label && (
        <label className="text-sm text-gray-700 dark:text-gray-300 min-w-[80px]">{label}</label>
      )}
      <div className="flex items-center gap-1">
        <select
          value={hours}
          onChange={handleHourChange}
          disabled={disabled}
          className="p-1 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          {Array.from({ length: 24 }, (_, i) => (
            <option key={i} value={i}>
              {i.toString().padStart(2, '0')}
            </option>
          ))}
        </select>
        <span className="text-gray-700 dark:text-gray-300">:</span>
        <select
          value={minutes}
          onChange={handleMinuteChange}
          disabled={disabled}
          className="p-1 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          {Array.from({ length: 60 }, (_, i) => (
            <option key={i} value={i}>
              {i.toString().padStart(2, '0')}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
