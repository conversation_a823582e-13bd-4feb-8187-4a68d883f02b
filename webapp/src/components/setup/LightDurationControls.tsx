import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { TimePickerWrapper } from './TimePickerWrapper';
import { CaptureActionSelect } from './CaptureActionSelect';
import { ModeSelect, LightMode } from './ModeSelect';
import { ControlModeSelect, ControlMode } from './ControlModeSelect';
import { LightStatusConfirm } from './LightStatusConfirm';

type CaptureAction = 'none' | 'on' | 'off';

interface LightDurationControlsProps {
  onLightConnectionChange: (lightIndex: number, isConnected: boolean) => void;
  onLightSettingsChange?: (lightIndex: number, settings: {
    startTime?: string,
    endTime?: string,
    captureAction?: CaptureAction,
    mode?: LightMode,
    controlMode?: ControlMode,
    pulseWidth?: number
  }) => void;
  onLightControlModeChange?: (lightIndex: number, mode: ControlMode, pulseWidth?: number) => void;
  onConfirmLightStatus?: (lightIndex: number, status: boolean) => void;
  disabled?: boolean;
  light1Connected?: boolean;
  light2Connected?: boolean;
  light3Connected?: boolean;
  light1ControlMode?: ControlMode;
  light2ControlMode?: ControlMode;
  light3ControlMode?: ControlMode;
  light1PulseWidth?: number;
  light2PulseWidth?: number;
  light3PulseWidth?: number;
  light1StatusKnown?: boolean;
  light2StatusKnown?: boolean;
  light3StatusKnown?: boolean;
}

interface LightState {
  isActive: boolean;
  collapsed: boolean;
  startTime: string;
  endTime: string;
  captureAction: CaptureAction;
  mode: LightMode;
  controlMode: ControlMode;
  pulseWidth: number;
  statusKnown: boolean;
  showStatusConfirm: boolean;
}

export function LightDurationControls({
  onLightConnectionChange,
  onLightSettingsChange,
  onLightControlModeChange,
  onConfirmLightStatus,
  disabled,
  light1Connected = false,
  light2Connected = false,
  light3Connected = false,
  light1ControlMode = 'direct',
  light2ControlMode = 'direct',
  light3ControlMode = 'direct',
  light1PulseWidth = 100,
  light2PulseWidth = 100,
  light3PulseWidth = 100,
  light1StatusKnown = true,
  light2StatusKnown = true,
  light3StatusKnown = true
}: LightDurationControlsProps) {
  // Determine which light should be expanded initially
  const getInitialCollapsedState = (index: number, isConnected: boolean) => {
    // If this light is connected and no other connected light has been found yet, expand it
    if (isConnected) {
      if (index === 0) return false; // First connected light is always expanded
      if (index === 1 && !light1Connected) return false; // Second light is expanded if first is not connected
      if (index === 2 && !light1Connected && !light2Connected) return false; // Third light is expanded if first and second are not connected
    }
    return true; // Otherwise collapse
  };

  // Default: first connected light is expanded; others are collapsed
  const [lights, setLights] = useState<LightState[]>([
    {
      isActive: light1Connected,
      collapsed: getInitialCollapsedState(0, light1Connected),
      startTime: '07:00',
      endTime: '19:00',
      captureAction: 'none',
      mode: 'scheduled',
      controlMode: light1ControlMode,
      pulseWidth: light1PulseWidth,
      statusKnown: light1StatusKnown,
      showStatusConfirm: light1ControlMode === 'toggle' && !light1StatusKnown
    },
    {
      isActive: light2Connected,
      collapsed: getInitialCollapsedState(1, light2Connected),
      startTime: '07:00',
      endTime: '19:00',
      captureAction: 'none',
      mode: 'scheduled',
      controlMode: light2ControlMode,
      pulseWidth: light2PulseWidth,
      statusKnown: light2StatusKnown,
      showStatusConfirm: light2ControlMode === 'toggle' && !light2StatusKnown
    },
    {
      isActive: light3Connected,
      collapsed: getInitialCollapsedState(2, light3Connected),
      startTime: '07:00',
      endTime: '19:00',
      captureAction: 'none',
      mode: 'scheduled',
      controlMode: light3ControlMode,
      pulseWidth: light3PulseWidth,
      statusKnown: light3StatusKnown,
      showStatusConfirm: light3ControlMode === 'toggle' && !light3StatusKnown
    },
  ]);

  // Update lights when connection status or control mode changes
  useEffect(() => {
    setLights(prev => {
      // Create new state with updated connection status and control mode
      const newLights = [
        {
          ...prev[0],
          isActive: light1Connected,
          controlMode: light1ControlMode,
          pulseWidth: light1PulseWidth,
          statusKnown: light1StatusKnown,
          showStatusConfirm: light1ControlMode === 'toggle' && !light1StatusKnown
        },
        {
          ...prev[1],
          isActive: light2Connected,
          controlMode: light2ControlMode,
          pulseWidth: light2PulseWidth,
          statusKnown: light2StatusKnown,
          showStatusConfirm: light2ControlMode === 'toggle' && !light2StatusKnown
        },
        {
          ...prev[2],
          isActive: light3Connected,
          controlMode: light3ControlMode,
          pulseWidth: light3PulseWidth,
          statusKnown: light3StatusKnown,
          showStatusConfirm: light3ControlMode === 'toggle' && !light3StatusKnown
        },
      ];

      // Check if any light is currently expanded
      const hasExpandedLight = newLights.some(light => light.isActive && !light.collapsed);

      // If no light is expanded but at least one is connected, expand the first connected one
      if (!hasExpandedLight) {
        if (light1Connected) {
          newLights[0].collapsed = false;
        } else if (light2Connected) {
          newLights[1].collapsed = false;
        } else if (light3Connected) {
          newLights[2].collapsed = false;
        }
      }

      return newLights;
    });
  }, [
    light1Connected, light2Connected, light3Connected,
    light1ControlMode, light2ControlMode, light3ControlMode,
    light1PulseWidth, light2PulseWidth, light3PulseWidth,
    light1StatusKnown, light2StatusKnown, light3StatusKnown
  ]);

  const updateLight = (index: number, updates: Partial<LightState>) => {
    setLights(prev => {
      const newLights = [...prev];
      const currentLight = { ...newLights[index] };

      // Special handling for mode changes
      if (updates.mode !== undefined) {
        // If changing to alwaysOn, set appropriate times
        if (updates.mode === 'alwaysOn') {
          updates.startTime = '00:00';
          updates.endTime = '23:59';
        }
        // If changing to alwaysOff, set appropriate times
        else if (updates.mode === 'alwaysOff') {
          updates.startTime = '00:00';
          updates.endTime = '00:00';
        }
      }

      // Check for identical start and end times in scheduled mode
      if (updates.startTime !== undefined || updates.endTime !== undefined) {
        const newStartTime = updates.startTime || currentLight.startTime;
        const newEndTime = updates.endTime || currentLight.endTime;

        // If in scheduled mode and times are identical, switch to appropriate mode
        if (currentLight.mode === 'scheduled' && newStartTime === newEndTime) {
          // If times are 00:00, switch to alwaysOff
          if (newStartTime === '00:00') {
            updates.mode = 'alwaysOff';
          }
          // Otherwise, switch to alwaysOn
          else {
            updates.mode = 'alwaysOn';
            updates.startTime = '00:00';
            updates.endTime = '23:59';
          }
        }
      }

      // Special handling for control mode changes
      if (updates.controlMode !== undefined) {
        // If switching to toggle mode, show status confirmation dialog
        if (updates.controlMode === 'toggle' && currentLight.controlMode === 'direct') {
          updates.statusKnown = false;
          updates.showStatusConfirm = true;
        }
        // If switching to direct mode, status is always known
        else if (updates.controlMode === 'direct') {
          updates.statusKnown = true;
          updates.showStatusConfirm = false;
        }
      }

      newLights[index] = { ...currentLight, ...updates };
      return newLights;
    });

    // Notify parent component of the change if relevant properties are updated
    if (onLightSettingsChange &&
        (updates.startTime !== undefined ||
         updates.endTime !== undefined ||
         updates.captureAction !== undefined ||
         updates.mode !== undefined)) {
      onLightSettingsChange(index, {
        startTime: updates.startTime,
        endTime: updates.endTime,
        captureAction: updates.captureAction,
        mode: updates.mode
      });
    }

    // Notify parent component of control mode changes
    if (onLightControlModeChange &&
        (updates.controlMode !== undefined || updates.pulseWidth !== undefined)) {
      onLightControlModeChange(
        index,
        updates.controlMode || lights[index].controlMode,
        updates.pulseWidth
      );
    }
  };

  // Handle light status confirmation
  const handleConfirmStatus = (index: number, status: boolean) => {
    // Update local state
    updateLight(index, {
      statusKnown: true,
      showStatusConfirm: false
    });

    // Notify parent component
    if (onConfirmLightStatus) {
      onConfirmLightStatus(index, status);
    }
  };

  // Handle canceling status confirmation
  const handleCancelConfirm = (index: number) => {
    // Just hide the dialog without changing status
    updateLight(index, {
      showStatusConfirm: false
    });
  };

  const toggleCollapse = (index: number) => {
    // Only allow expansion if the light is active
    if (!lights[index].isActive) {
      return; // Do nothing for inactive lights
    }

    // If the light is already collapsed, expand it and collapse all others
    if (lights[index].collapsed) {
      setLights(prev => {
        return prev.map((light, i) => ({
          ...light,
          collapsed: i !== index // Collapse all except the clicked one
        }));
      });
    } else {
      // If the light is already expanded, just collapse it
      updateLight(index, { collapsed: true });
    }
  };

  const toggleActive = (index: number) => {
    const newActive = !lights[index].isActive;

    if (newActive) {
      // If activating a light, expand it and collapse all others
      setLights(prev => {
        const newLights = prev.map((light, i) => ({
          ...light,
          collapsed: i !== index, // Collapse all except the activated one
          isActive: i === index ? newActive : light.isActive
        }));
        return newLights;
      });
    } else {
      // If deactivating a light, just update its state
      updateLight(index, { isActive: newActive });
    }

    onLightConnectionChange(index, newActive);
  };

  return (
    <div className="space-y-4">
      {lights.map((light, i) => {
        return (
          <div
            key={i}
            className={`border rounded-lg p-4 ${light.isActive ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300 dark:border-gray-700'}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => toggleCollapse(i)}
                  className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
                  disabled={disabled}
                >
                  {light.collapsed ? (
                    <ChevronRight className="w-5 h-5" />
                  ) : (
                    <ChevronDown className="w-5 h-5" />
                  )}
                </button>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  Light {i + 1}
                </h3>
              </div>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={light.isActive}
                  onChange={() => toggleActive(i)}
                  disabled={disabled}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                  {light.isActive ? 'Connected' : 'Disconnected'}
                </span>
              </label>
            </div>

            {!light.collapsed && (
              <div
                className={`mt-4 space-y-4 ${light.isActive ? '' : 'opacity-50 pointer-events-none'}`}
              >
                <div className="grid grid-cols-1 gap-4">
                  <ModeSelect
                    value={light.mode}
                    onChange={(mode) => updateLight(i, { mode })}
                    disabled={disabled || !light.isActive}
                    label=""
                    groupName={`light-${i+1}-mode`}
                  />

                  {light.mode === 'scheduled' && (
                    <>
                      <TimePickerWrapper
                        value={light.startTime}
                        onChange={(time) => updateLight(i, { startTime: time })}
                        disabled={disabled || !light.isActive}
                        label="On time:"
                      />

                      <TimePickerWrapper
                        value={light.endTime}
                        onChange={(time) => updateLight(i, { endTime: time })}
                        disabled={disabled || !light.isActive}
                        label="Off time:"
                      />
                    </>
                  )}

                  <CaptureActionSelect
                    value={light.captureAction}
                    onChange={(action) => updateLight(i, { captureAction: action })}
                    disabled={disabled || !light.isActive}
                    label="At capture:"
                  />

                  <div className="border-t pt-4 mt-4 border-gray-200 dark:border-gray-700">
                    <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-3">
                      Control Settings
                    </h4>

                    <ControlModeSelect
                      value={light.controlMode}
                      pulseWidth={light.pulseWidth}
                      onChange={(mode, pulseWidth) => updateLight(i, { controlMode: mode, pulseWidth })}
                      disabled={disabled || !light.isActive}
                      label="Control mode:"
                    />

                    {light.controlMode === 'toggle' && !light.statusKnown && (
                      <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/30 rounded-md border border-yellow-200 dark:border-yellow-800">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200">
                          Please confirm the current status of Light {i + 1} to synchronize.
                        </p>
                        <div className="mt-2 flex gap-2">
                          <button
                            onClick={() => handleConfirmStatus(i, false)}
                            className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                          >
                            Light is OFF
                          </button>
                          <button
                            onClick={() => handleConfirmStatus(i, true)}
                            className="px-2 py-1 text-xs bg-yellow-500 text-white rounded hover:bg-yellow-600"
                          >
                            Light is ON
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Status confirmation dialog */}
            {light.showStatusConfirm && (
              <LightStatusConfirm
                lightNumber={i + 1}
                isOpen={light.showStatusConfirm}
                onConfirm={(status) => handleConfirmStatus(i, status)}
                onCancel={() => handleCancelConfirm(i)}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
