import { useState } from 'react';
import { Download, Archive, Loader2, Trash2 } from 'lucide-react';
import { formatBytes } from '../utils/storage';
import { ZipFile } from '../types';

interface FileManagementPanelProps {
  zipFiles: ZipFile[];
  hasUnzippedImages: boolean;
  unzippedImagesCount: number;
  onDownloadZip: (filename: string) => Promise<void>;
  onCreateNewZip: () => Promise<void>;
  onDeleteZip?: (filename: string) => Promise<void>;
}

export function FileManagementPanel({
  zipFiles,
  hasUnzippedImages,
  unzippedImagesCount,
  onDownloadZip,
  onCreateNewZip,
  onDeleteZip
}: FileManagementPanelProps) {
  const [isZipping, setIsZipping] = useState(false);
  const [downloadingFile, setDownloadingFile] = useState<string | null>(null);
  const [fileToDelete, setFileToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDownload = async (filename: string) => {
    setDownloadingFile(filename);
    try {
      await onDownloadZip(filename);
    } finally {
      setDownloadingFile(null);
    }
  };

  const handleCreateZip = async () => {
    setIsZipping(true);
    try {
      await onCreateNewZip();
    } finally {
      setIsZipping(false);
    }
  };

  const handleDeleteClick = (filename: string) => {
    setFileToDelete(filename);
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (fileToDelete && onDeleteZip) {
      try {
        await onDeleteZip(fileToDelete);
      } catch (error) {
        console.error('Error deleting file:', error);
      } finally {
        setShowDeleteConfirm(false);
        setFileToDelete(null);
      }
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg max-h-[32rem] flex flex-col">
      <h2 className="text-lg font-semibold dark:text-white mb-4">File Management</h2>

      {/* Make table section scrollable */}
      <div className="flex-1 min-h-0"> {/* min-h-0 allows flex child to shrink */}
        <div className="overflow-hidden">
          <div className="overflow-y-auto max-h-[20rem] scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-white dark:bg-gray-800 sticky top-0 z-10">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    File Name
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    Size
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    Created
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {zipFiles.map((file) => (
                  <tr key={file.name}>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                      {file.name}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                      {formatBytes(file.size)}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                      {new Date(file.created).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 text-right flex gap-2 justify-end">
                      <button
                        onClick={() => handleDownload(file.name)}
                        disabled={downloadingFile === file.name}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50"
                        title="Download zip file"
                      >
                        {downloadingFile === file.name ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : (
                          <Download className="w-5 h-5" />
                        )}
                      </button>
                      {onDeleteZip && (
                        <button
                          onClick={() => handleDeleteClick(file.name)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete zip file"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
                {zipFiles.length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-4 py-2 text-center text-gray-500 dark:text-gray-400">
                      No zip files available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Keep bottom section fixed */}
      <div className="pt-4 mt-4 border-t dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium dark:text-gray-200">
            {unzippedImagesCount} Unzipped Images
          </span>
          <button
            onClick={handleCreateZip}
            disabled={!hasUnzippedImages || isZipping}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
          >
            {isZipping ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Creating Zip...
              </>
            ) : (
              <>
                <Archive className="w-4 h-4" />
                Zip and Download
              </>
            )}
          </button>
        </div>

        {!hasUnzippedImages && (
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            No unzipped images available.
          </p>
        )}

        {isZipping && (
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Creating zip file. This might take a while...
          </p>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full shadow-xl">
            <h3 className="text-lg font-semibold mb-4 dark:text-white">Confirm Delete</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to delete <span className="font-semibold">{fileToDelete}</span>? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelDelete}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}