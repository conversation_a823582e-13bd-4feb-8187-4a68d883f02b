import React, { useMemo, useState } from 'react';
import { ParentSize } from '@visx/responsive';
import { scaleLinear } from '@visx/scale';
import { AxisBottom, AxisLeft } from '@visx/axis';
import { localPoint } from '@visx/event';
import { Circle } from '@visx/shape';
import { Tooltip } from '@visx/tooltip';

export interface TempRhScatterProps {
  tempHistory?: number[];   // in ℃
  rhHistory?: number[];     // in %
  darkMode: boolean;
}

// thresholds (kPa)
const VPD_MOIST   = 0.5;
const VPD_IDEAL   = 1.2;
const VPD_EXTREME = 1.6;

// SVP and RH‐threshold
function svp(T: number) {
  return 0.6108 * Math.exp((17.27 * T) / (T + 237.3));
}
function rhThresh(T: number, vpd: number) {
  const r = 100 * (1 - vpd / svp(T));
  return Math.max(0, Math.min(100, r));
}

// Tooltip data type
interface TooltipData {
  temp: number;
  rh: number;
  vpd: number;
  zone: string;
  x: number;
  y: number;
}

export function TempRhScatter({
  tempHistory = [],
  rhHistory = [],
  darkMode
}: TempRhScatterProps) {
  // State for tooltip
  const [tooltipData, setTooltipData] = useState<TooltipData | null>(null);

  // Zip them safely
  const points = useMemo(() => {
    const n = Math.min(tempHistory.length, rhHistory.length);
    const out: { temp: number; rh: number; age: number }[] = [];

    for (let i = 0; i < n; i++) {
      const t = tempHistory[i];
      const h = rhHistory[i];
      if (typeof t === 'number' && typeof h === 'number' && h >= 0 && h <= 100) {
        // Add age property (0 = newest, 1 = oldest)
        const age = i / (n - 1 || 1);
        out.push({ temp: t, rh: h, age });
      }
    }

    // Sort by age so newer points are drawn on top
    return out.sort((a, b) => b.age - a.age);
  }, [tempHistory, rhHistory]);

  // if no valid points, show placeholder
  if (!points.length) {
    return (
      <div style={{
        height: 200,
        background: darkMode ? '#111' : '#fff',
        color:      darkMode ? '#888' : '#666',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        Waiting for sensor data…
      </div>
    );
  }

  // Sampled T-grid
  const temps = Array.from({ length: 21 }, (_, i) => 15 + i); // 15→35°C

  // Precompute curves
  const curveMoist  = temps.map(t => ({ t, rh: rhThresh(t, VPD_MOIST)   }));
  const curveIdeal  = temps.map(t => ({ t, rh: rhThresh(t, VPD_IDEAL)   }));
  const curveExtreme= temps.map(t => ({ t, rh: rhThresh(t, VPD_EXTREME) }));

  // Colors
  const bg    = darkMode ? '#111' : '#fff';
  const moish = '#3b82f6';
  const ideal = '#22c55e';
  const dry   = '#ef4444';
  const extr  = '#f97316';
  const axis  = darkMode ? '#888' : '#666';
  const grid  = axis + '33';
  const dotFill   = darkMode ? '#fff' : '#000';
  const dotStroke = darkMode ? '#000' : '#fff';

  return (
    <div style={{ width: '100%', height: 300, background: bg, position: 'relative' }}>
      {tooltipData && (
        <Tooltip
          top={tooltipData.y - 120}
          left={tooltipData.x + 10}
          style={{
            backgroundColor: darkMode ? '#333' : '#fff',
            color: darkMode ? '#fff' : '#333',
            padding: '8px 12px',
            borderRadius: '4px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
            fontSize: '12px',
            border: `1px solid ${darkMode ? '#555' : '#ddd'}`,
            pointerEvents: 'none',
            zIndex: 100,
          }}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Sensor Reading</div>
          <div>Temperature: <strong>{tooltipData.temp.toFixed(1)}°C</strong></div>
          <div>Humidity: <strong>{tooltipData.rh.toFixed(0)}%</strong></div>
          <div>VPD: <strong>{tooltipData.vpd.toFixed(2)} kPa</strong></div>
          <div style={{ marginTop: '4px' }}>Zone: <strong>{tooltipData.zone}</strong></div>
        </Tooltip>
      )}
      <ParentSize>
        {({ width, height }) => {
          const margin = { top: 20, right: 20, bottom: 40, left: 40 };
          const innerWidth  = width  - margin.left - margin.right;
          const innerHeight = height - margin.top  - margin.bottom;

          // scales
          const xScale = scaleLinear<number>({
            domain: [15,35], range: [0, innerWidth],
          });
          const yScale = scaleLinear<number>({
            domain: [0,100], range: [innerHeight, 0],
          });

          // path builder
          function buildBandPath(
            top: {t:number;rh:number}[],
            bot: {t:number;rh:number}[]
          ) {
            let d = '';
            bot.forEach((p,i) => {
              const x = xScale(p.t), y = yScale(p.rh);
              d += i===0 ? `M${x},${y}` : `L${x},${y}`;
            });
            top.slice().reverse().forEach(p => {
              const x = xScale(p.t), y = yScale(p.rh);
              d += `L${x},${y}`;
            });
            return d + 'Z';
          }

          // band paths
          const top100   = temps.map(t=>({t, rh:100}));
          const bot0     = temps.map(t=>({t, rh:0}));
          const pathMoist   = buildBandPath(top100,  curveMoist);
          const pathIdeal   = buildBandPath(curveIdeal,  curveMoist);
          const pathTooDry  = buildBandPath(curveExtreme, curveIdeal);
          const pathExtreme = buildBandPath(bot0,       curveExtreme);

          return (
            <svg width={width} height={height}>
              <g transform={`translate(${margin.left},${margin.top})`}>
                {/* VPD bands */}
                <path d={pathMoist}   fill={moish} pointerEvents="none" />
                <path d={pathIdeal}   fill={ideal} pointerEvents="none" />
                <path d={pathTooDry}  fill={dry}   pointerEvents="none" />
                <path d={pathExtreme} fill={extr}  pointerEvents="none" />

                {/* grid */}
                {yScale.ticks(5).map((v,i)=>
                  <line key={i}
                    x1={0} x2={innerWidth}
                    y1={yScale(v)} y2={yScale(v)}
                    stroke={grid}
                  />
                )}
                {xScale.ticks(5).map((v,i)=>
                  <line key={i}
                    x1={xScale(v)} x2={xScale(v)}
                    y1={0} y2={innerHeight}
                    stroke={grid}
                  />
                )}

                {/* data points */}
                {points.map((p,i)=>
                  <Circle key={i}
                    cx={xScale(p.temp)}
                    cy={yScale(p.rh)}
                    r={6}
                    fill={dotFill}
                    fillOpacity={1 - p.age * 0.7} // Fade older points
                    stroke={dotStroke}
                    strokeWidth={2}
                    onMouseMove={(e)=>{
                      // Calculate VPD for this point
                      const svpValue = svp(p.temp);
                      const vpdValue = svpValue * (1 - p.rh / 100);

                      // Determine zone
                      let zone = "";
                      if (vpdValue < VPD_MOIST) {
                        zone = "Too Moist";
                      } else if (vpdValue < VPD_IDEAL) {
                        zone = "Ideal";
                      } else if (vpdValue < VPD_EXTREME) {
                        zone = "Too Dry";
                      } else {
                        zone = "Extreme Dry";
                      }

                      // Get mouse position
                      const point = localPoint(e);
                      if (point) {
                        setTooltipData({
                          temp: p.temp,
                          rh: p.rh,
                          vpd: vpdValue,
                          zone,
                          x: point.x,
                          y: point.y
                        });
                      }
                    }}
                    onMouseLeave={()=>{ setTooltipData(null); }}
                  />
                )}
              </g>

              {/* axes */}
              <AxisBottom
                scale={xScale}
                top={margin.top + innerHeight}
                left={margin.left}
                stroke={axis}
                tickStroke={axis}
                tickLabelProps={()=>({ fill: axis, fontSize:10, dy:8 })}
              />
              <AxisLeft
                scale={yScale}
                left={margin.left}
                top={margin.top}
                stroke={axis}
                tickStroke={axis}
                tickLabelProps={()=>({ fill: axis, fontSize:10, dx:-8 })}
              />
            </svg>
          );
        }}
      </ParentSize>
    </div>
  );
}

export default TempRhScatter;
