import { HardDrive } from 'lucide-react';
import { formatBytes, getStorageColorClass } from '../../utils/storage';
import type { StorageInfo } from '../../types';

interface StorageStatusProps {
  storage: StorageInfo;
}

export function StorageStatus({ storage }: StorageStatusProps) {
  const colorClass = getStorageColorClass(storage.percentage);
  
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <HardDrive className={colorClass} size={20} />
        <span className="dark:text-gray-200">
          Storage: {storage.percentage}% used
        </span>
      </div>
      
      <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div 
          className={`h-full ${colorClass.replace('text-', 'bg-')} transition-all`}
          style={{ width: `${storage.percentage}%` }}
        />
      </div>
      
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {formatBytes(storage.used)} of {formatBytes(storage.total)}
      </div>
    </div>
  );
}