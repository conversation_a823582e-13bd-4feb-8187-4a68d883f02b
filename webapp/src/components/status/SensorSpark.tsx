import { Thermometer, Droplets } from 'lucide-react';
import { useRef, useEffect, useCallback } from 'react';

interface SensorSparkProps {
  label: string;
  unit: string;
  value: number | null;
  data: number[];
  color: string;
  icon?: 'temp' | 'humidity';
}

export function SensorSpark({ label, unit, value, data, color, icon }: SensorSparkProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const MAX_DATA_POINTS = 30; // Maximum number of data points to display - adjusted for better visibility

  // Function to draw the sparkline
  const drawSparkline = useCallback(() => {
    if (!canvasRef.current || !data.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions to match its display size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Get the actual color value from the Tailwind class
    let fillColor = '';
    if (color === 'text-green-500') {
      fillColor = '#22c55e'; // Tailwind green-500
    } else if (color === 'text-blue-500') {
      fillColor = '#3b82f6'; // Tailwind blue-500
    } else {
      fillColor = '#9ca3af'; // Default gray
    }

    // Set the fill color
    ctx.fillStyle = fillColor;

    // Get the data to display (last MAX_DATA_POINTS if more than that)
    const displayData = data.slice(-MAX_DATA_POINTS);

    // Calculate the width of each bar based on the canvas width and maximum number of data points
    // Each bar has a fixed width regardless of how many data points we currently have
    const barWidth = canvas.width / MAX_DATA_POINTS;

    // Find the max value for scaling
    const maxValue = Math.max(...displayData, 1); // Ensure at least 1 to avoid division by zero

    // Draw each bar - new data points enter from the left
    // Reverse the array to draw newest points on the left
    [...displayData].reverse().forEach((value, index) => {
      // Calculate position - newest points (at the start of reversed array) are on the left
      const x = index * barWidth;
      // Scale height to 90% of canvas height to leave some margin
      const barHeight = (value / maxValue) * (canvas.height * 0.9);
      const y = canvas.height - barHeight;

      // Draw the bar with no gap between bars (subtract a small amount to ensure no gaps)
      ctx.fillRect(x, y, barWidth - 0.5, barHeight);
    });
  }, [data, color, MAX_DATA_POINTS]);

  // Draw the sparkline bars on the canvas
  useEffect(() => {
    drawSparkline();

    // Add resize listener to redraw when window size changes
    const handleResize = () => {
      drawSparkline();
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, color]);

  // If no data or value is null, show a placeholder
  if (!data?.length || value === null) {
    return (
      <div className="flex items-center py-1">
        <div className="flex items-center w-[130px] justify-start">
          {icon === 'temp' && <Thermometer className="text-gray-400" size={18} />}
          {icon === 'humidity' && <Droplets className="text-gray-400" size={18} />}
          <span className="dark:text-gray-300 ml-2 text-left whitespace-nowrap">{label}: --{unit}</span>
        </div>
        <div className="flex-grow ml-2 h-5 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
          <span className="text-xs text-gray-400">No data available</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center py-1">
      <div className="flex items-center w-[130px] justify-start">
        {icon === 'temp' && <Thermometer className={color} size={18} />}
        {icon === 'humidity' && <Droplets className={color} size={18} />}
        <span className={`${color} font-medium ml-2 text-left whitespace-nowrap`}>
          {label}: {value}{unit}
        </span>
      </div>
      <div className="flex-grow ml-2">
        <canvas
          ref={canvasRef}
          className="w-full h-5"
        />
      </div>
    </div>
  );
}
