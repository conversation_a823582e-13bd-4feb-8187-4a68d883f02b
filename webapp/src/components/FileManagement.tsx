import { useState } from 'react';
import { Download, Archive, Loader2 } from 'lucide-react';
import { formatBytes } from '../utils/storage';

interface ZipFile {
  name: string;
  size: number;
  created: string;
}

interface FileManagementProps {
  zipFiles: ZipFile[];
  hasUnzippedImages: boolean;
  onDownloadZip: (filename: string) => Promise<void>;
  onCreateNewZip: () => Promise<void>;
}

export function FileManagement({
  zipFiles,
  hasUnzippedImages,
  onDownloadZip,
  onCreateNewZip
}: FileManagementProps) {
  const [isZipping, setIsZipping] = useState(false);
  const [downloadingFile, setDownloadingFile] = useState<string | null>(null);

  const handleDownload = async (filename: string) => {
    setDownloadingFile(filename);
    try {
      await onDownloadZip(filename);
    } finally {
      setDownloadingFile(null);
    }
  };

  const handleCreateZip = async () => {
    setIsZipping(true);
    try {
      await onCreateNewZip();
    } finally {
      setIsZipping(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold dark:text-white">File Management</h1>

      {/* Zip Files Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  File Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Size
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {zipFiles.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No zip files available
                  </td>
                </tr>
              ) : (
                zipFiles.map((file) => (
                  <tr key={file.name}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {file.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatBytes(file.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(file.created).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <button
                        onClick={() => handleDownload(file.name)}
                        disabled={downloadingFile === file.name}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50"
                        title="Download zip file"
                      >
                        {downloadingFile === file.name ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : (
                          <Download className="w-5 h-5" />
                        )}
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Unzipped Images Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold dark:text-white">Unzipped Images</h2>
          <button
            onClick={handleCreateZip}
            disabled={!hasUnzippedImages || isZipping}
            className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
          >
            {isZipping ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                Creating Zip...
              </>
            ) : (
              <>
                <Archive className="w-5 h-5" />
                Zip and Download
              </>
            )}
          </button>
        </div>
        
        {!hasUnzippedImages && (
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            No unzipped images available.
          </p>
        )}
        
        {isZipping && (
          <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            Creating zip file. This might take a while depending on the number of images...
          </p>
        )}
      </div>
    </div>
  );
}