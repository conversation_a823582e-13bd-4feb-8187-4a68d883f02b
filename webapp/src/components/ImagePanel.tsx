import { useState } from 'react';
import type { TimelapseImage } from '../types';

interface ImagePanelProps {
  firstImage: TimelapseImage;
  lastImage: TimelapseImage;
}

export function ImagePanel({ firstImage, lastImage }: ImagePanelProps) {
  const [firstImageError, setFirstImageError] = useState(false);
  const [lastImageError, setLastImageError] = useState(false);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
      <h2 className="text-lg font-semibold mb-4 dark:text-white">Captured Images</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div className="space-y-2">
          <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden relative">
            {firstImage && !firstImageError ? (
              <img
                src={firstImage.url}
                alt="First capture"
                className="w-full h-full object-cover"
                loading="lazy"
                onError={() => setFirstImageError(true)}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                No image available
              </div>
            )}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
            {firstImage.timestamp}
          </p>
        </div>
        <div className="space-y-2">
          <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
            {lastImage && !lastImageError ? (
              <img
                src={lastImage.url}
                alt="Latest capture"
                className="w-full h-full object-cover"
                loading="lazy"
                onError={() => setLastImageError(true)}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                No image available
              </div>
            )}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
            {lastImage.timestamp}
          </p>
        </div>
      </div>
    </div>
  );
}