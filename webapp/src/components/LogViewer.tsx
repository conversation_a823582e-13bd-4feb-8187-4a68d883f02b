import React, { useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import type { LogEntry } from '../types';

interface LogViewerProps {
  logs: LogEntry[];
  logLevel: 'info' | 'warning' | 'error';
  onLogLevelChange: (level: 'info' | 'warning' | 'error') => void;
  isExpanded: boolean;
  onToggle: () => void;
}

export function LogViewer({ logs, logLevel, onLogLevelChange, isExpanded, onToggle }: LogViewerProps) {
  const logsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isExpanded && logsRef.current) {
      logsRef.current.scrollTop = logsRef.current.scrollHeight;
    }
  }, [logs, isExpanded]);

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-500';
      case 'warning': return 'text-yellow-500';
      case 'info': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mt-4">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 text-left"
      >
        <h2 className="text-lg font-semibold dark:text-white">System Logs</h2>
        {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </button>

      {isExpanded && (
        <>
          <div className="flex justify-center gap-2 p-2 border-t dark:border-gray-700">
            <button
              onClick={() => onLogLevelChange('error')}
              className={`flex items-center gap-1 px-3 py-1 rounded-md ${logLevel === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'}`}
            >
              <AlertCircle size={16} />
              Error
            </button>
            <button
              onClick={() => onLogLevelChange('warning')}
              className={`flex items-center gap-1 px-3 py-1 rounded-md ${logLevel === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'}`}
            >
              <AlertTriangle size={16} />
              Warning
            </button>
            <button
              onClick={() => onLogLevelChange('info')}
              className={`flex items-center gap-1 px-3 py-1 rounded-md ${logLevel === 'info' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'}`}
            >
              <Info size={16} />
              Info
            </button>
          </div>

          <div
            ref={logsRef}
            className="max-h-60 overflow-y-auto p-4 border-t dark:border-gray-700"
          >
            {logs
              .filter(log => {
                // Filter logs based on selected level
                if (logLevel === 'error') return log.level === 'error';
                if (logLevel === 'warning') return ['error', 'warning'].includes(log.level);
                return true; // 'info' shows all logs
              })
              .map((log, index) => (
                <div key={index} className="py-1">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {log.timestamp}
                  </span>
                  <span className={`ml-2 ${getLevelColor(log.level)}`}>
                    [{log.level.toUpperCase()}]
                  </span>
                  <span className="ml-2 dark:text-gray-200">{log.message}</span>
                </div>
              ))}
          </div>
        </>
      )}
    </div>
  );
}