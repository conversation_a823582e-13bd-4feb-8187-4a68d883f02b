import React from 'react';
import { ImagePanel } from './ImagePanel';
import type { TimelapseImage, Settings } from '../types';

interface ImageSectionProps {
  firstImage: TimelapseImage;
  lastImage: TimelapseImage;
  isRunning: boolean;
  isPaused: boolean;
  onSettingsChange: (settings: Settings) => void;
}

export function ImageSection({
  firstImage,
  lastImage,
  isRunning,
  isPaused,
  onSettingsChange
}: ImageSectionProps) {
  return (
    <div className="space-y-4">
      <ImagePanel firstImage={firstImage} lastImage={lastImage} />
    </div>
  );
}