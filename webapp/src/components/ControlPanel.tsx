import React from 'react';
import {
  Play,
  Pause,
  Square,
  Camera,
  Lightbulb
} from 'lucide-react';
import { VideoRecordButton } from './controls/VideoRecordButton';
import { FrequencySlider } from './setup/FrequencySlider';

interface ControlPanelProps {
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onCapture: () => void;
  onFrequencyChange: (value: number) => void;
  isRunning: boolean;
  isPaused: boolean;
  isCameraConnected: boolean;
  frequency: number;
  isLight1Active: boolean;
  isLight2Active: boolean;
  isLight3Active: boolean;
  light1Connected: boolean;
  light2Connected: boolean;
  light3Connected: boolean;
  socket: any; // Add socket as a prop
  networkConnected?: boolean; // Add network connection status
  isRecording?: boolean; // Add video recording status
}

export function ControlPanel({
  onStart,
  onPause,
  onStop,
  onCapture,
  onFrequencyChange,
  isRunning,
  isPaused,
  isCameraConnected,
  frequency,
  isLight1Active,
  isLight2Active,
  isLight3Active,
  light1Connected,
  light2Connected,
  light3Connected,
  socket, // Destructure socket from props
  networkConnected = true, // Default to true for backward compatibility
  isRecording = false, // Default to false for backward compatibility
}: ControlPanelProps) {

  // Debug log the props
  console.log('ControlPanel props:', {
    isCameraConnected,
    networkConnected,
    light1Connected,
    light2Connected,
    light3Connected,
    isRecording
  });

  const handleAction = (action: () => void, confirmMessage?: string) => {
    if (confirmMessage && !window.confirm(confirmMessage)) {
      return;
    }
    action();
  };

  const handleStart = () => {
    if (isPaused) {
      handleAction(onStart, 'Are you sure you want to resume the timelapse?');
    } else {
      onStart();
    }
  };

  const handleToggleRecording = () => {
    console.log('Emitting toggleVideo event');
    socket.emit('toggleVideo');
    // The state will be updated when we receive the videoStatus event from the server
  };

  const handleToggleLight1 = () => {
    socket.emit('toggleLight1');
  };

  const handleToggleLight2 = () => {
    socket.emit('toggleLight2');
  };

  const handleToggleLight3 = () => {
    socket.emit('toggleLight3');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
      <h2 className="text-lg font-semibold mb-4 dark:text-white">Controls</h2>
      <div className="space-y-3">
        <button
          onClick={handleStart}
          disabled={!networkConnected || !isCameraConnected || (isRunning && !isPaused)}
          title={!networkConnected ? "Server connection required" : !isCameraConnected ? "Camera must be connected to start timelapse" : ""}
          className="w-full flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded-lg py-2 px-4 transition-colors"
        >
          <Play size={20} /> {isPaused ? 'Resume Timelapse' : 'Start Timelapse'}
        </button>
        <button
          onClick={() => handleAction(onPause)}
          disabled={!networkConnected || !isRunning || isPaused}
          className="w-full flex items-center justify-center gap-2 bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-300 text-white rounded-lg py-2 px-4 transition-colors"
        >
          <Pause size={20} /> Pause Timelapse
        </button>
        <button
          onClick={() => handleAction(onStop, 'Are you sure you want to stop the timelapse?')}
          disabled={!networkConnected || !isRunning}
          className="w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white rounded-lg py-2 px-4 transition-colors"
        >
          <Square size={20} /> Stop Timelapse
        </button>
        <button
          onClick={() => handleAction(onCapture)}
          disabled={!networkConnected || !isCameraConnected}
          title={!networkConnected ? "Server connection required" : !isCameraConnected ? "Camera must be connected to capture images" : ""}
          className="w-full flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg py-2 px-4 transition-colors disabled:cursor-not-allowed"
        >
          <Camera size={20} /> Capture Image
        </button>

        <VideoRecordButton
          isRecording={isRecording}
          onToggleRecording={handleToggleRecording}
          disabled={!networkConnected || !isCameraConnected || (isRunning && !isPaused)}
          disabledReason={
            !networkConnected ? "Server connection required"
            : !isCameraConnected ? "Camera must be connected to record video"
            : (isRunning && !isPaused) ? "Pause or stop the timelapse to record video"
            : ""
          }
        />

        <div className="flex justify-between space-x-2">
          <button
            onClick={() => handleAction(handleToggleLight1)}
            disabled={!networkConnected || !light1Connected}
            className={`flex-1 flex items-center justify-center gap-2 ${
              !light1Connected ? 'bg-gray-400 cursor-not-allowed' :
              isLight1Active ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-gray-500 hover:bg-gray-600'
            } text-white rounded-lg py-2 px-4 transition-colors`}
          >
            <Lightbulb size={20} /> Light 1
          </button>
          <button
            onClick={() => handleAction(handleToggleLight2)}
            disabled={!networkConnected || !light2Connected}
            className={`flex-1 flex items-center justify-center gap-2 ${
              !light2Connected ? 'bg-gray-400 cursor-not-allowed' :
              isLight2Active ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-gray-500 hover:bg-gray-600'
            } text-white rounded-lg py-2 px-4 transition-colors`}
          >
            <Lightbulb size={20} /> Light 2
          </button>
          <button
            onClick={() => handleAction(handleToggleLight3)}
            disabled={!networkConnected || !light3Connected}
            className={`flex-1 flex items-center justify-center gap-2 ${
              !light3Connected ? 'bg-gray-400 cursor-not-allowed' :
              isLight3Active ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-gray-500 hover:bg-gray-600'
            } text-white rounded-lg py-2 px-4 transition-colors`}
          >
            <Lightbulb size={20} /> Light 3
          </button>
        </div>
        <div className="border-t border-gray-300 dark:border-gray-700 my-3"></div>
        <FrequencySlider
          value={frequency}
          onChange={onFrequencyChange}
          disabled={!networkConnected || (isRunning && !isPaused)}
        />
      </div>
    </div>
  );
}