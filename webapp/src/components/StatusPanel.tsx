import {
  Camera,
  Lightbulb,
  Image as ImageIcon,
  // Clock, // Uncomment when Next Capture feature is implemented
  Server
} from 'lucide-react';
import { StorageStatus } from './status/StorageStatus';
import { SensorSpark } from './status/SensorSpark';
import { TempRhScatter } from './status/TempRhScatter';
import type { Settings } from '../types';

interface StatusPanelProps {
  data: Settings;
}

export function StatusPanel({ data }: StatusPanelProps) {
  // Debug log the data
  console.log('StatusPanel data:', data);
  const getLightStatus = (isConnected: boolean, state: boolean) => {
    if (!isConnected) return "--";
    return state ? "On" : "Off";
  };

  // Check if we're in dark mode
  const isDarkMode = document.documentElement.classList.contains('dark');

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
      <h2 className="text-lg font-semibold mb-4 dark:text-white">System Status</h2>
      <div className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Camera className={data.isCameraConnected ? "text-green-500" : "text-red-500"} size={20} />
            <span className="dark:text-gray-200">Camera: {data.isCameraConnected ? 'Connected' : 'Not Detected'}
              {data.isCameraConnected &&
                <span className="ml-1 text-xs text-green-500">(Ready)</span>}
            </span>
          </div>

        {/* Camera Status with better visibility */}
        {/* <div className="grid grid-cols-2 gap-y-3 gap-x-2 mt-4">
          <div className="col-span-2 flex items-center gap-2 p-2 rounded-md bg-opacity-10 border border-opacity-20"
            style={{
              backgroundColor: data.isCameraConnected ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)',
              borderColor: data.isCameraConnected ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'
            }}
          >
            <Camera
              className={data.isCameraConnected ? "text-green-500" : "text-red-500"}
              size={24}
            />
            <div>
              <span className="font-medium dark:text-gray-200">
                {data.isCameraConnected ? 'Camera Connected' : 'Camera Not Connected'}
              </span>
              {!data.isCameraConnected && (
                <p className="text-xs text-red-500 mt-1">
                  Please connect your camera to use capture and timelapse features
                </p>
              )}
            </div>
          </div> */}

          <div className="flex items-center gap-2" title="Connection status to the Raspberry Pi backend server">
            <Server className={data.networkStatus ? "text-green-500" : "text-red-500"} size={20} />
            <span className="dark:text-gray-200">Backend: {data.networkStatus ? 'Connected' : 'Disconnected'}</span>
            {!data.networkStatus && (
              <span className="ml-1 text-xs text-red-500">(Server unavailable)</span>
            )}
          </div>
          <div className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              <ImageIcon className="text-blue-500" size={20} />
              <span className="dark:text-gray-200">Total Images: {data.imagesTaken}</span>
            </div>
            <div className="flex items-center gap-2 pl-6">
              <span className="text-xs dark:text-gray-300">Timelapse: {data.timelapseImagesTaken}</span>
            </div>
            <div className="flex items-center gap-2 pl-6">
              <span className="text-xs dark:text-gray-300">Manual: {data.manualImagesTaken}</span>
            </div>
          </div>
          {/* Next Capture feature not implemented yet
          <div className="flex items-center gap-2">
            <Clock className="text-purple-500" size={20} />
            <span className="dark:text-gray-200">Next Capture: {data.nextCapture || 'N/A'}</span>
          </div>
          */}
        </div>

        <div className="pt-2 border-t dark:border-gray-700 space-y-3">
          <div className="flex items-center gap-2">
            <Lightbulb
              className={!data.light1Connected ? "text-gray-400" :
                data.light1State ? "text-yellow-500" : "text-gray-400"}
              size={20}
            />
            <span className="dark:text-gray-200">
              Light 1: {getLightStatus(data.light1Connected, data.light1State)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Lightbulb
              className={!data.light2Connected ? "text-gray-400" :
                data.light2State ? "text-yellow-500" : "text-gray-400"}
              size={20}
            />
            <span className="dark:text-gray-200">
              Light 2: {getLightStatus(data.light2Connected, data.light2State)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Lightbulb
              className={!data.light3Connected ? "text-gray-400" :
                data.light3State ? "text-yellow-500" : "text-gray-400"}
              size={20}
            />
            <span className="dark:text-gray-200">
              Light 3: {getLightStatus(data.light3Connected, data.light3State)}
            </span>
          </div>
        </div>

        <div className="pt-2 border-t dark:border-gray-700">
          <StorageStatus storage={data.storage} />
        </div>

        {/* DHT22 Sensor Data */}
        <div className="pt-2 border-t dark:border-gray-700">
          <h3 className="text-sm font-medium mb-2 dark:text-gray-200">Sensor Data</h3>
          <div className="space-y-1">
            <SensorSpark
              label="Temp"
              unit="°C"
              value={data.currentTemp || null}
              data={data.tempHistory || []}
              color="text-green-500"
              icon="temp"
            />
            <SensorSpark
              label="Hum"
              unit="%"
              value={data.currentRH || null}
              data={data.rhHistory || []}
              color="text-blue-500"
              icon="humidity"
            />
          </div>

          {/* Temperature-Humidity Scatter Plot with VPD Zones */}
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2 dark:text-gray-200">VPD Comfort Zones</h3>
            <TempRhScatter
              tempHistory={data.tempHistory || []}
              rhHistory={data.rhHistory || []}
              darkMode={isDarkMode}
            />
          </div>
        </div>
      </div>
    </div>
  );
}