import { useState, useCallback, useRef } from 'react';
import { Settings } from '../types';

const DEFAULT_SETTINGS: Settings = {
  frequency: 15,
  light1StartTime: '07:00',
  light2StartTime: '07:00',
  light3StartTime: '07:00',
  light1EndTime: '19:00',
  light2EndTime: '19:00',
  light3EndTime: '19:00',
  light1CaptureAction: 'none',
  light2CaptureAction: 'none',
  light3CaptureAction: 'none',
  light1Mode: 'scheduled',
  light2Mode: 'scheduled',
  light3Mode: 'scheduled',
  isCameraConnected: false,
  light1State: false,
  light2State: false,
  light3State: false,
  light1Connected: false,
  light2Connected: false,
  light3Connected: false,
  timelapseActive: false,
  timelapsePaused: false,
  networkStatus: false, // Indicates connection to the backend server
  imagesTaken: 0, // Total images taken
  timelapseImagesTaken: 0, // Images taken during timelapse
  manualImagesTaken: 0, // Images taken manually
  nextCapture: "Not scheduled",
  storage: {
    total: 0,
    used: 0,
    percentage: 0,
  },
  availableImages: 0,
  // zipFiles: [],
  logsExpanded: true
};

export function useTimelapseSettings() {
  const [settings, setSettings] = useState<Settings>(() => {
    const saved = localStorage.getItem('timelapseSettings');
    return saved ? JSON.parse(saved) : DEFAULT_SETTINGS;
  });

  // Keep track of settings when pause started
  const pausedSettings = useRef<Settings | null>(null);

  const updateSettings = useCallback((newSettings: Partial<Settings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      localStorage.setItem('timelapseSettings', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const setPausedSettings = useCallback((settings: Settings | null) => {
    pausedSettings.current = settings;
  }, []);

  const hasSettingsChanged = useCallback(() => {
    if (!pausedSettings.current) return false;

    // Check if any settings have changed
    return (
      settings.frequency !== pausedSettings.current.frequency ||
      settings.light1StartTime !== pausedSettings.current.light1StartTime ||
      settings.light1EndTime !== pausedSettings.current.light1EndTime ||
      settings.light1CaptureAction !== pausedSettings.current.light1CaptureAction ||
      settings.light2StartTime !== pausedSettings.current.light2StartTime ||
      settings.light2EndTime !== pausedSettings.current.light2EndTime ||
      settings.light2CaptureAction !== pausedSettings.current.light2CaptureAction ||
      settings.light3StartTime !== pausedSettings.current.light3StartTime ||
      settings.light3EndTime !== pausedSettings.current.light3EndTime ||
      settings.light3CaptureAction !== pausedSettings.current.light3CaptureAction
    );
  }, [settings]);

  const confirmSettingsChange = useCallback(async () => {
    return window.confirm(
      'Warning: Settings have been modified during pause. These changes may affect timelapse consistency. Continue with new settings?'
    );
  }, []);

  return {
    settings,
    updateSettings,
    setPausedSettings,
    hasSettingsChanged,
    confirmSettingsChange
  };
}