from flask import Flask, send_file, jsonify, request
from pathlib import Path
import zipfile
import os
from datetime import datetime
import io
import json
import threading
import time

app = Flask(__name__)

IMAGES_DIR = Path("images")
ZIPS_DIR = Path("zips")

# System state
class SystemState:
    def __init__(self):
        self.light_status = False
        self.is_recording = False
        self.timelapse_active = False
        self.timelapse_paused = False
        self.capture_thread = None
        self.next_capture = None
        self.capture_interval = 15  # minutes
        self.camera_connected = True  # Default state
        self.network_status = True    # Default state
        self.last_diagnostic = None

system = SystemState()

# Hardware diagnostics placeholders
def check_camera_connection() -> bool:
    """
    Placeholder for checking if camera is connected and responding.
    In reality, this would:
    1. Check if camera device exists (e.g., /dev/video0)
    2. Try to open a connection to the camera
    3. Run a test capture
    4. Check camera properties
    """
    # Simulate camera check
    print("Running camera diagnostics...")
    return system.camera_connected

def check_network_status() -> bool:
    """
    Placeholder for checking network connectivity.
    In reality, this would:
    1. Ping default gateway
    2. Check internet connectivity
    3. Verify API server access
    """
    # Simulate network check
    print("Checking network status...")
    return system.network_status

def get_storage_info() -> dict:
    """
    Placeholder for getting storage information.
    In reality, this would:
    1. Check disk space using os.statvfs
    2. Monitor disk I/O
    3. Verify write permissions
    """
    total = 32 * 1024 * 1024 * 1024  # 32GB
    used = sum(f.stat().st_size for f in IMAGES_DIR.glob("*"))
    percentage = (used / total) * 100 if total > 0 else 0
    
    return {
        "total": total,
        "used": used,
        "percentage": round(percentage, 1)
    }

# Hardware control placeholders
def control_light(status: bool) -> bool:
    """
    Placeholder for controlling the physical light.
    In reality, this would:
    1. Interface with GPIO
    2. Send signal to light controller
    3. Verify light state changed
    4. Handle PWM if dimming is supported
    """
    print(f"Light {'ON' if status else 'OFF'}")
    system.light_status = status
    return status

def capture_image_from_camera() -> bool:
    """
    Placeholder for capturing an image from the physical camera.
    In reality, this would:
    1. Set camera parameters (exposure, focus, etc.)
    2. Capture raw image
    3. Process image (compression, metadata)
    4. Save to storage
    """
    if not check_camera_connection():
        print("Camera not connected!")
        return False

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dummy_image_path = IMAGES_DIR / f"capture_{timestamp}.jpg"
    
    # Create an empty file to simulate image
    dummy_image_path.touch()
    print(f"Image captured: {dummy_image_path}")
    return True

def start_video_recording() -> bool:
    """
    Placeholder for starting video recording.
    In reality, this would:
    1. Initialize video encoder
    2. Set recording parameters
    3. Start capture stream
    4. Monitor recording status
    """
    if not check_camera_connection():
        return False
    
    print("Video recording started")
    system.is_recording = True
    return True

def stop_video_recording() -> bool:
    """
    Placeholder for stopping video recording.
    In reality, this would:
    1. Stop capture stream
    2. Finalize video file
    3. Release encoder resources
    """
    print("Video recording stopped")
    system.is_recording = False
    return True

def run_system_diagnostics() -> dict:
    """
    Placeholder for running full system diagnostics.
    In reality, this would check all system components.
    """
    system.last_diagnostic = datetime.now()
    
    return {
        "camera": check_camera_connection(),
        "network": check_network_status(),
        "storage": get_storage_info(),
        "timestamp": system.last_diagnostic.isoformat()
    }

# Timelapse control
def timelapse_worker():
    """Background worker for timelapse capture"""
    while system.timelapse_active:
        if not system.timelapse_paused:
            if capture_image_from_camera():
                # Update next capture time
                system.next_capture = datetime.now().timestamp() + (system.capture_interval * 60)
            else:
                print("Failed to capture image, pausing timelapse")
                system.timelapse_paused = True
            time.sleep(system.capture_interval * 60)  # Wait for next capture
        else:
            time.sleep(1)  # Check pause status every second

# API Endpoints
@app.route("/api/status", methods=["GET"])
def get_status():
    diagnostics = run_system_diagnostics()
    return jsonify({
        "isConnected": diagnostics["camera"],
        "lightStatus": system.light_status,
        "networkStatus": diagnostics["network"],
        "totalImages": len(list(IMAGES_DIR.glob("*.jpg"))),
        "nextCapture": datetime.fromtimestamp(system.next_capture).isoformat() if system.next_capture else None,
        "storage": diagnostics["storage"],
        "lastDiagnostic": diagnostics["timestamp"]
    })

@app.route("/api/timelapse/start", methods=["POST"])
def start_timelapse():
    if not check_camera_connection():
        return jsonify({"status": "error", "message": "Camera not connected"}), 400
        
    system.timelapse_active = True
    system.timelapse_paused = False
    
    if not system.capture_thread or not system.capture_thread.is_alive():
        system.capture_thread = threading.Thread(target=timelapse_worker)
        system.capture_thread.daemon = True
        system.capture_thread.start()
    
    return jsonify({"status": "started"})

@app.route("/api/timelapse/pause", methods=["POST"])
def pause_timelapse():
    system.timelapse_paused = True
    return jsonify({"status": "paused"})

@app.route("/api/timelapse/stop", methods=["POST"])
def stop_timelapse():
    system.timelapse_active = False
    system.timelapse_paused = False
    if system.capture_thread:
        system.capture_thread.join(timeout=1)
    return jsonify({"status": "stopped"})

@app.route("/api/capture", methods=["POST"])
def capture_image():
    success = capture_image_from_camera()
    return jsonify({"status": "captured" if success else "failed"})

@app.route("/api/light/toggle", methods=["POST"])
def toggle_light():
    new_status = not system.light_status
    success = control_light(new_status)
    return jsonify({"lightStatus": success})

@app.route("/api/video/toggle", methods=["POST"])
def toggle_video():
    if system.is_recording:
        success = stop_video_recording()
    else:
        success = start_video_recording()
    return jsonify({"isRecording": system.is_recording if success else not system.is_recording})

# [Previous endpoints remain unchanged: get_latest_images, get_settings, update_settings, get_logs, get_files, download_zip, create_zip]

if __name__ == "__main__":
    IMAGES_DIR.mkdir(exist_ok=True)
    ZIPS_DIR.mkdir(exist_ok=True)
    app.run(debug=True)