const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { logger, setIo } = require('./logger');
const path = require('path');
const cors = require('cors');
const fs = require('fs');
const { initializeGPIO, cleanup } = require('./hardware/rpi_gpio');
const { initializeDHT22, setIo: setDHT22Io } = require('./hardware/dht22');
const { keepNetworkAlive } = require('./sockets/network');
const { keepCameraAwake } = require('./sockets/camera');
const { PUBLIC_DIR, IMAGES_DIR, ZIPS_DIR } = require('./sockets/constants');

keepNetworkAlive();
keepCameraAwake();

const PUBLIC_IMAGES_DIR = path.join(PUBLIC_DIR, 'images');

// Ensure directories exist
[PUBLIC_DIR, PUBLIC_IMAGES_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    logger.info(`Created directory: ${dir}`);
  }
});

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*', // Allow all origins
    methods: ['GET', 'POST'],
  },
});

// Set the io instance in the logger for real-time log streaming
setIo(io);

// Set the io instance in the DHT22 module for real-time updates
setDHT22Io(io);

// Initialize GPIO on startup
initializeGPIO().then(success => {
  if (!success) {
    logger.error('Failed to initialize GPIO. Light control may not work.');
  }
});

// Initialize DHT22 sensor
initializeDHT22().then(success => {
  if (!success) {
    logger.error('Failed to initialize DHT22 sensor. Temperature and humidity readings may not be available.');
  } else {
    logger.info('DHT22 sensor initialized successfully.');
  }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  cleanup();
  process.exit();
});

// Middleware
app.use(cors()); // Enable CORS
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(PUBLIC_DIR));
app.use('/images', express.static(PUBLIC_IMAGES_DIR));

// Socket.IO setup
require('./sockets/index')(io);

// Example route
app.get('/', (req, res) => {
  res.send('Welcome to the Socket.IO server!');
});

// Export the server for use in server.js
module.exports = { app, server };