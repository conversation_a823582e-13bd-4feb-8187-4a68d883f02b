const fs = require('fs');
const path = require('path');
const { logger } = require('../logger');
const { IMAGES_DIR, ZIPS_DIR } = require('./constants');
const { checkCameraConnection, captureImageWithRetry, startVideoRecording, stopVideoRecording } = require('./camera');
// const { checkNetworkStatus } = require('./network'); // Imported elsewhere
const { createZipOfImages } = require('./storage');
const { timelapseWorker, turnOffAllLights, initializeLightSchedules } = require('./timelapse');
const { emitUpdatedStatus } = require('./utils');
const systemState = require('./state');
const { controlLight } = require('./light');
// const { data } = require('autoprefixer'); // Unused import

// Ensure directories exist
if (!fs.existsSync(IMAGES_DIR)) fs.mkdirSync(IMAGES_DIR);
if (!fs.existsSync(ZIPS_DIR)) fs.mkdirSync(ZIPS_DIR);

module.exports = (io) => {
    io.on('connection', (socket) => {
        logger.info('A user connected:', socket.id);

        socket.on('getStatus', () => {
            emitUpdatedStatus(socket);
        });

        socket.on('startTimelapse', (settings) => {
            logger.info('Received startTimelapse event');
            if (!checkCameraConnection()) {
                socket.emit('error', { message: "Camera not connected" });
                return;
            }

            // Zip any remaining pictures in the directory
            if (fs.readdirSync(IMAGES_DIR).length > 0) {
                createZipOfImages();
            }
            // Reset image tracking when starting new session
            systemState.firstImagePath = null;
            systemState.lastImagePath = null;
            systemState.firstImageTimestamp = null;
            systemState.lastImageTimestamp = null;

            systemState.timelapseActive = true;
            systemState.timelapsePaused = false;
            // Initialize lastCaptureTime to now when starting a timelapse
            systemState.lastCaptureTime = Date.now();
            // Do not reset counters when starting a timelapse
            // This allows manual images captured before starting a timelapse to be part of the same session

            logger.info('Starting timelapse - Keeping existing image counters');
            logger.info(`Current counters: manual=${systemState.manualImagesTaken}, timelapse=${systemState.timelapseImagesTaken}, total=${systemState.imagesTaken}`);
            systemState.captureInterval = settings.frequency; // Set capture interval from settings

            // Update light schedule settings
            systemState.light1StartTime = settings.light1StartTime || '07:00';
            systemState.light2StartTime = settings.light2StartTime || '07:00';
            systemState.light3StartTime = settings.light3StartTime || '07:00';
            systemState.light1EndTime = settings.light1EndTime || '19:00';
            systemState.light2EndTime = settings.light2EndTime || '19:00';
            systemState.light3EndTime = settings.light3EndTime || '19:00';

            // Update light capture action settings
            systemState.light1CaptureAction = settings.light1CaptureAction || 'none';
            systemState.light2CaptureAction = settings.light2CaptureAction || 'none';
            systemState.light3CaptureAction = settings.light3CaptureAction || 'none';

            // Update light mode settings
            systemState.light1Mode = settings.light1Mode || 'scheduled';
            systemState.light2Mode = settings.light2Mode || 'scheduled';
            systemState.light3Mode = settings.light3Mode || 'scheduled';

            logger.info(`Light 1: mode=${systemState.light1Mode}, schedule=${systemState.light1StartTime}-${systemState.light1EndTime}, capture action=${systemState.light1CaptureAction}`);
            logger.info(`Light 2: mode=${systemState.light2Mode}, schedule=${systemState.light2StartTime}-${systemState.light2EndTime}, capture action=${systemState.light2CaptureAction}`);
            logger.info(`Light 3: mode=${systemState.light3Mode}, schedule=${systemState.light3StartTime}-${systemState.light3EndTime}, capture action=${systemState.light3CaptureAction}`);

            // Initialize the 24-hour light schedules
            logger.info('Initializing 24-hour light schedules');
            initializeLightSchedules(socket);

            // Schedule the first capture immediately
            if (!systemState.captureThread) {
                logger.info('Scheduling first capture immediately');
                systemState.captureThread = setTimeout(() => timelapseWorker(socket), 1000); // Start almost immediately
            }
            socket.emit('timelapseStatus', { status: "started" });
            emitUpdatedStatus(socket); // Send updated status to refresh counters
        });

        socket.on('pauseTimelapse', () => {
            logger.info('Received pauseTimelapse event');
            systemState.timelapsePaused = true;

            // Note: We don't turn off lights when pausing, as they might be needed for inspection
            // The lights will follow their normal schedule or can be manually controlled
            logger.info('Timelapse paused - lights remain in current state');

            socket.emit('timelapseStatus', { status: "paused" });
            emitUpdatedStatus(socket); // Send updated status to refresh counters
        });

        socket.on('resumeTimelapse', (settings) => {
            logger.info('Received resumeTimelapse event');

            // Update capture interval if provided in settings
            if (settings && settings.frequency) {
                const oldInterval = systemState.captureInterval;
                systemState.captureInterval = settings.frequency;
                logger.info(`Updating capture frequency from ${oldInterval} to ${settings.frequency} minutes`);
            }

            // Calculate elapsed time since last capture (in milliseconds)
            const now = Date.now();
            const lastCaptureTime = systemState.lastCaptureTime || now;
            const elapsedSinceLastCapture = now - lastCaptureTime;

            // Convert current capture interval to milliseconds (using the potentially updated interval)
            const captureIntervalMs = systemState.captureInterval * 60 * 1000;

            // Calculate delay until next capture
            let delayUntilNextCapture = captureIntervalMs - elapsedSinceLastCapture;

            // If the delay is negative or zero, capture immediately
            if (delayUntilNextCapture <= 0) {
                delayUntilNextCapture = 1000; // Capture almost immediately (1 second)
                logger.info('Resuming timelapse - Capture due or overdue, capturing immediately');
            } else {
                logger.info(`Resuming timelapse - Next capture in ${Math.round(delayUntilNextCapture / 1000)} seconds (${Math.round(delayUntilNextCapture / 60000)} minutes)`);
            }

            // Resume the timelapse
            systemState.timelapsePaused = false;

            // Clear any existing capture thread
            if (systemState.captureThread) {
                clearTimeout(systemState.captureThread);
            }

            // Schedule the next capture based on the calculated delay
            systemState.captureThread = setTimeout(() => timelapseWorker(socket), delayUntilNextCapture);

            socket.emit('timelapseStatus', { status: "resumed" });
            emitUpdatedStatus(socket); // Send updated status to refresh counters
        });

        socket.on('stopTimelapse', async () => {
            logger.info('Received stopTimelapse event');
            systemState.timelapseActive = false;
            systemState.timelapsePaused = false;
            // Reset lastCaptureTime when stopping a timelapse
            systemState.lastCaptureTime = null;
            // Reset ALL counters to zero when stopping a timelapse
            systemState.timelapseImagesTaken = 0;
            systemState.manualImagesTaken = 0;
            systemState.imagesTaken = 0; // Reset total count as well

            logger.info('Stopping timelapse - Reset all image counters to zero');
            logger.info(`Counters after reset: manual=${systemState.manualImagesTaken}, timelapse=${systemState.timelapseImagesTaken}, total=${systemState.imagesTaken}`);

            // Clear any scheduled capture
            if (systemState.captureThread) {
                clearTimeout(systemState.captureThread);
                systemState.captureThread = null;
            }

            // Turn off all lights
            logger.info('Turning off all lights when stopping timelapse');
            await turnOffAllLights(socket);

            // Only create zip if there are images present
            if (fs.readdirSync(IMAGES_DIR).length > 0) {
                const zipFilename = createZipOfImages(); // Create zip file when timelapse is stopped
                logger.info(`Created zip file: ${zipFilename}`);
            }

            socket.emit('timelapseStatus', { status: "stopped" });
            emitUpdatedStatus(socket); // Send updated status to refresh counters
        });

        socket.on('createZip', () => {
            logger.info('Received createZip event');
            const zipFilename = createZipOfImages(); // Create zip file
            socket.emit('zipCreated', { zipFilename });
            emitUpdatedStatus(socket); // Send updated status
        });

        socket.on('deleteZip', (data) => {
            const { filename } = data;
            logger.info(`Received deleteZip event for file: ${filename}`);

            try {
                const filePath = path.join(ZIPS_DIR, filename);

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    logger.error(`File not found: ${filePath}`);
                    socket.emit('error', { message: `File not found: ${filename}` });
                    return;
                }

                // Delete the file
                fs.unlinkSync(filePath);
                logger.info(`Successfully deleted file: ${filename}`);

                // Send success response
                socket.emit('zipDeleted', { filename });

                // Update status to refresh file list
                emitUpdatedStatus(socket);
            } catch (error) {
                logger.error(`Error deleting file ${filename}:`, error);
                socket.emit('error', { message: `Error deleting file: ${error.message}` });
            }
        });

        socket.on('captureImage', () => {
            logger.info('Received captureImage event');

            // Check camera connection first
            if (!checkCameraConnection()) {
                logger.error('Camera not connected, cannot capture image');
                socket.emit('captureStatus', {
                    status: "failed",
                    error: "Camera not connected"
                });
                return;
            }

            // Pass true to indicate this is a manual capture
            const success = captureImageWithRetry(socket, 3, true);
            socket.emit('captureStatus', {
                status: success ? "captured" : "failed",
                timestamp: new Date().toISOString(),
                manualImagesTaken: systemState.manualImagesTaken, // Send the updated manual count
                totalImagesTaken: systemState.imagesTaken // Send the total count
            });
            emitUpdatedStatus(socket); // Send updated status to refresh images and counters
        });

        socket.on('setLightConnection', (data) => {
            logger.info('Received setLightConnection event', data);
            const { lightNumber, isConnected } = data;

            if (lightNumber >= 1 && lightNumber <= 3) {
                systemState[`light${lightNumber}Connected`] = isConnected;
                systemState.saveConfig(); // Save configuration to file
                logger.info(`Light ${lightNumber} connection set to ${isConnected ? 'connected' : 'disconnected'}`);
                emitUpdatedStatus(socket);
            }
        });

        socket.on('updateLightControlMode', (data) => {
            logger.info('Received updateLightControlMode event', data);
            const { lightNumber, mode, pulseWidth } = data;

            if (lightNumber >= 1 && lightNumber <= 3) {
                systemState[`light${lightNumber}ControlMode`] = mode;
                if (pulseWidth) systemState[`light${lightNumber}PulseWidth`] = pulseWidth;

                // If switching to toggle mode, mark status as unknown until confirmed
                if (mode === 'toggle') {
                    systemState[`light${lightNumber}StatusKnown`] = false;
                } else {
                    systemState[`light${lightNumber}StatusKnown`] = true;
                }

                systemState.saveConfig(); // Save configuration to file
                logger.info(`Light ${lightNumber} control mode set to ${mode}${pulseWidth ? `, pulse width: ${pulseWidth}ms` : ''}`);
                emitUpdatedStatus(socket);
            }
        });

        socket.on('confirmLightStatus', (data) => {
            logger.info('Received confirmLightStatus event', data);
            const { lightNumber, status } = data;

            if (lightNumber >= 1 && lightNumber <= 3) {
                systemState[`light${lightNumber}Status`] = status;
                systemState[`light${lightNumber}StatusKnown`] = true;

                systemState.saveConfig(); // Save configuration to file
                logger.info(`Light ${lightNumber} status confirmed as ${status ? 'ON' : 'OFF'}`);
                emitUpdatedStatus(socket);
            }
        });

        socket.on('toggleLight1', async (data) => {
            logger.info('Received toggleLight1 event', data);

            // Get the control mode and pulse width
            const mode = systemState.light1ControlMode;
            const pulseWidth = systemState.light1PulseWidth;

            if (mode === 'direct') {
                // Direct mode - use forceState if provided, otherwise toggle
                const newStatus = data?.forceState !== undefined ? data.forceState : !systemState.light1Status;
                const success = await controlLight(1, newStatus, { mode: 'direct' });
                if (!success) {
                    logger.error('Failed to toggle light 1 (direct mode)');
                }
            } else if (mode === 'toggle') {
                // Toggle mode - send a pulse
                const success = await controlLight(1, true, { mode: 'toggle', pulseWidth });
                if (!success) {
                    logger.error(`Failed to toggle light 1 (toggle mode, ${pulseWidth}ms)`);
                }
            }

            emitUpdatedStatus(socket);
        });

        socket.on('toggleLight2', async (data) => {
            logger.info('Received toggleLight2 event', data);

            // Get the control mode and pulse width
            const mode = systemState.light2ControlMode;
            const pulseWidth = systemState.light2PulseWidth;

            if (mode === 'direct') {
                // Direct mode - use forceState if provided, otherwise toggle
                const newStatus = data?.forceState !== undefined ? data.forceState : !systemState.light2Status;
                const success = await controlLight(2, newStatus, { mode: 'direct' });
                if (!success) {
                    logger.error('Failed to toggle light 2 (direct mode)');
                }
            } else if (mode === 'toggle') {
                // Toggle mode - send a pulse
                const success = await controlLight(2, true, { mode: 'toggle', pulseWidth });
                if (!success) {
                    logger.error(`Failed to toggle light 2 (toggle mode, ${pulseWidth}ms)`);
                }
            }

            emitUpdatedStatus(socket);
        });

        socket.on('toggleLight3', async (data) => {
            logger.info('Received toggleLight3 event', data);

            // Get the control mode and pulse width
            const mode = systemState.light3ControlMode;
            const pulseWidth = systemState.light3PulseWidth;

            if (mode === 'direct') {
                // Direct mode - use forceState if provided, otherwise toggle
                const newStatus = data?.forceState !== undefined ? data.forceState : !systemState.light3Status;
                const success = await controlLight(3, newStatus, { mode: 'direct' });
                if (!success) {
                    logger.error('Failed to toggle light 3 (direct mode)');
                }
            } else if (mode === 'toggle') {
                // Toggle mode - send a pulse
                const success = await controlLight(3, true, { mode: 'toggle', pulseWidth });
                if (!success) {
                    logger.error(`Failed to toggle light 3 (toggle mode, ${pulseWidth}ms)`);
                }
            }

            emitUpdatedStatus(socket);
        });

        socket.on('toggleVideo', () => {
            logger.info('Received toggleVideo event');

            // Check if timelapse is active and not paused
            if (systemState.timelapseActive && !systemState.timelapsePaused) {
                logger.info('Cannot toggle video while timelapse is active and not paused');
                socket.emit('error', { message: 'Cannot record video while timelapse is active. Please pause or stop the timelapse first.' });
                return;
            }

            // Check camera connection
            if (!checkCameraConnection()) {
                logger.error('Camera not connected, cannot toggle video');
                socket.emit('error', { message: 'Camera not connected' });
                return;
            }

            const success = systemState.isRecording ?
                stopVideoRecording(socket) :
                startVideoRecording(socket);

            logger.info(`Video recording ${systemState.isRecording ? 'started' : 'stopped'}, success: ${success}`);
            socket.emit('videoStatus', { isRecording: systemState.isRecording });
            emitUpdatedStatus(socket); // Send updated status
        });

        socket.on('disconnect', () => {
            logger.info('User disconnected:', socket.id);
        });
    });
};