const { captureImageWithRetry } = require('./camera');
const { emitUpdatedStatus } = require('./utils');
const { logger } = require('../logger');
const systemState = require('./state');
const { controlLight } = require('./light');

// Store light timers for the 24-hour cycle
let lightTimers = {
    // Daily timers for each light
    daily: [null, null, null],
    // Timers for turning lights back to their scheduled state after capture
    restore: [null, null, null]
};

/**
 * Calculate the next ON and OFF times for a light based on the 24-hour schedule
 * @param {number} lightNum - Light number (1, 2, or 3)
 * @returns {Object} Object with nextOnTime and nextOffTime in milliseconds since epoch
 */
function calculateLightSchedule(lightNum) {
    // Get the mode for this light
    const mode = systemState[`light${lightNum}Mode`] || 'scheduled';

    // Calculate the current time
    const now = new Date();

    // For debugging, log the exact current time
    logger.info(`Current time for Light ${lightNum} calculation: ${now.toLocaleTimeString()}`);

    // Handle different modes
    if (mode === 'alwaysOn') {
        // Always ON mode - light should always be ON
        logger.info(`Light ${lightNum} mode: Always ON`);

        // Set a far future transition time (24 hours from now)
        const nextTransitionTime = new Date(now);
        nextTransitionTime.setHours(nextTransitionTime.getHours() + 24);

        return {
            shouldBeOn: true,
            nextTransitionTime,
            timeUntilTransition: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
        };
    } else if (mode === 'alwaysOff') {
        // Always OFF mode - light should always be OFF
        logger.info(`Light ${lightNum} mode: Always OFF`);

        // Set a far future transition time (24 hours from now)
        const nextTransitionTime = new Date(now);
        nextTransitionTime.setHours(nextTransitionTime.getHours() + 24);

        return {
            shouldBeOn: false,
            nextTransitionTime,
            timeUntilTransition: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
        };
    }

    // Scheduled mode - use start and end times
    const startTimeStr = systemState[`light${lightNum}StartTime`] || '07:00';
    const endTimeStr = systemState[`light${lightNum}EndTime`] || '19:00';

    // Parse the time strings
    const [startHour, startMinute] = startTimeStr.split(':').map(Number);
    const [endHour, endMinute] = endTimeStr.split(':').map(Number);

    // Log the scheduled ON/OFF times
    logger.info(`Light ${lightNum} mode: Scheduled, ON from ${startTimeStr} to ${endTimeStr}`);

    // Create Date objects for today's ON and OFF times
    const todayOnTime = new Date(now);
    todayOnTime.setHours(startHour, startMinute, 0, 0);

    const todayOffTime = new Date(now);
    todayOffTime.setHours(endHour, endMinute, 0, 0);

    // If end time is before start time, it means the OFF time is tomorrow
    if (endHour < startHour || (endHour === startHour && endMinute < startMinute)) {
        todayOffTime.setDate(todayOffTime.getDate() + 1);
    }

    // Determine if the light should be ON or OFF right now
    let shouldBeOn;
    let nextTransitionTime;

    // If ON time is before OFF time (same day schedule)
    if (todayOnTime < todayOffTime) {
        shouldBeOn = now >= todayOnTime && now < todayOffTime;
        if (shouldBeOn) {
            // Currently ON, next transition is to OFF
            nextTransitionTime = todayOffTime;
        } else {
            // Currently OFF, next transition is to ON
            // If current time is after today's ON time, schedule for tomorrow
            if (now >= todayOnTime) {
                const tomorrowOnTime = new Date(todayOnTime);
                tomorrowOnTime.setDate(tomorrowOnTime.getDate() + 1);
                nextTransitionTime = tomorrowOnTime;
            } else {
                nextTransitionTime = todayOnTime;
            }
        }
    }
    // If ON time is after OFF time (overnight schedule)
    else {
        // Check if we're in the ON period (from start time today to end time tomorrow)
        const yesterdayOnTime = new Date(todayOnTime);
        yesterdayOnTime.setDate(yesterdayOnTime.getDate() - 1);

        shouldBeOn = now >= todayOnTime || now < todayOffTime;

        if (shouldBeOn) {
            // Currently ON, next transition is to OFF
            nextTransitionTime = todayOffTime;
        } else {
            // Currently OFF, next transition is to ON
            nextTransitionTime = todayOnTime;
        }
    }

    // Calculate the time until the next transition
    const timeUntilTransition = nextTransitionTime.getTime() - now.getTime();
    const minutesUntilTransition = Math.round(timeUntilTransition / 1000 / 60 * 100) / 100; // Round to 2 decimal places

    logger.info(`Light ${lightNum} status: currently ${shouldBeOn ? 'ON' : 'OFF'}, next transition to ${shouldBeOn ? 'OFF' : 'ON'} at ${nextTransitionTime.toLocaleTimeString()} (${minutesUntilTransition} minutes from now)`);

    return {
        shouldBeOn,
        nextTransitionTime,
        timeUntilTransition
    };
}

/**
 * Set a light to its scheduled state based on the 24-hour cycle
 * @param {number} lightNum - Light number (1, 2, or 3)
 * @param {Object} socket - Socket.io socket for sending updates
 */
async function setLightToScheduledState(lightNum, socket) {
    // Check if light is connected
    const isConnected = systemState[`light${lightNum}Connected`];
    if (!isConnected) {
        return; // Skip unconnected lights
    }

    // Calculate the light schedule
    const { shouldBeOn } = calculateLightSchedule(lightNum);

    // Get current light status and control mode
    const currentStatus = systemState[`light${lightNum}Status`];
    const controlMode = systemState[`light${lightNum}ControlMode`] || 'direct';
    const pulseWidth = systemState[`light${lightNum}PulseWidth`] || 100;

    // Only change the light state if it doesn't match the schedule
    if (currentStatus !== shouldBeOn) {
        try {
            if (controlMode === 'direct') {
                // Direct mode - set pin to high or low
                await controlLight(lightNum, shouldBeOn, { mode: 'direct' });
                logger.info(`Light ${lightNum} set to scheduled state: ${shouldBeOn ? 'ON' : 'OFF'} (direct mode)`);
            } else if (controlMode === 'toggle') {
                // Toggle mode - send a pulse to toggle the light
                await controlLight(lightNum, true, { mode: 'toggle', pulseWidth });
                logger.info(`Light ${lightNum} toggled to scheduled state: ${shouldBeOn ? 'ON' : 'OFF'} (toggle mode)`);
            }

            // Update UI
            if (socket) {
                emitUpdatedStatus(socket);
            }
        } catch (error) {
            logger.error(`Failed to set Light ${lightNum} to scheduled state: ${error.message}`);
        }
    } else {
        logger.info(`Light ${lightNum} already in scheduled state: ${shouldBeOn ? 'ON' : 'OFF'}`);
    }

    // Schedule the next transition
    scheduleLightTransition(lightNum, socket);
}

/**
 * Schedule the next light transition based on the 24-hour cycle
 * @param {number} lightNum - Light number (1, 2, or 3)
 * @param {Object} socket - Socket.io socket for sending updates
 */
function scheduleLightTransition(lightNum, socket) {
    // Clear any existing timer for this light
    if (lightTimers.daily[lightNum - 1]) {
        logger.info(`Clearing existing timer for Light ${lightNum}`);
        clearTimeout(lightTimers.daily[lightNum - 1]);
        lightTimers.daily[lightNum - 1] = null;
    }

    // Log the current light settings
    logger.info(`Light ${lightNum} settings: mode=${systemState[`light${lightNum}Mode`]}, capture action=${systemState[`light${lightNum}CaptureAction`]}`);

    // Calculate the light schedule
    const { shouldBeOn, nextTransitionTime, timeUntilTransition } = calculateLightSchedule(lightNum);

    // Schedule the next transition
    lightTimers.daily[lightNum - 1] = setTimeout(() => {
        logger.info(`Light ${lightNum} scheduled transition time reached, setting to ${!shouldBeOn ? 'ON' : 'OFF'}`);
        setLightToScheduledState(lightNum, socket);
    }, timeUntilTransition);

    logger.info(`Scheduled next transition for Light ${lightNum} at ${nextTransitionTime.toLocaleTimeString()} (${timeUntilTransition / 1000 / 60} minutes from now)`);
}

/**
 * Handle lights for capture based on force settings
 * @param {number} lightNum - Light number (1, 2, or 3)
 * @param {Object} socket - Socket.io socket for sending updates
 */
async function handleLightForCapture(lightNum, socket) {
    // Check if light is connected
    const isConnected = systemState[`light${lightNum}Connected`];
    if (!isConnected) {
        return; // Skip unconnected lights
    }

    // Get current light status
    const currentStatus = systemState[`light${lightNum}Status`];

    // Get capture action setting and mode
    const captureAction = systemState[`light${lightNum}CaptureAction`] || 'none';
    const lightMode = systemState[`light${lightNum}Mode`] || 'scheduled';
    const controlMode = systemState[`light${lightNum}ControlMode`] || 'direct';
    const pulseWidth = systemState[`light${lightNum}PulseWidth`] || 100;

    // Log the current state, mode, and capture action
    logger.info(`Light ${lightNum} capture settings: current=${currentStatus ? 'ON' : 'OFF'}, lightMode=${lightMode}, controlMode=${controlMode}, captureAction=${captureAction}`);

    // Clear any existing restore timer
    if (lightTimers.restore[lightNum - 1]) {
        clearTimeout(lightTimers.restore[lightNum - 1]);
        lightTimers.restore[lightNum - 1] = null;
    }

    // Apply capture action
    if (captureAction === 'off') {
        // Force OFF for capture
        try {
            // Only send command if state is changing
            if (currentStatus) {
                if (controlMode === 'direct') {
                    // Direct mode - set pin to low
                    await controlLight(lightNum, false, { mode: 'direct' });
                    logger.info(`Light ${lightNum} forced OFF for capture (direct mode, was ON)`);
                } else if (controlMode === 'toggle') {
                    // Toggle mode - send a pulse to toggle from ON to OFF
                    await controlLight(lightNum, true, { mode: 'toggle', pulseWidth });
                    logger.info(`Light ${lightNum} toggled for capture to turn OFF (toggle mode, was ON)`);
                }
            } else {
                logger.info(`Light ${lightNum} already OFF for capture`);
            }

            // Schedule restoration to scheduled state after capture
            lightTimers.restore[lightNum - 1] = setTimeout(() => {
                logger.info(`Restoring Light ${lightNum} to scheduled state after capture`);
                setLightToScheduledState(lightNum, socket);
            }, 5000); // Wait 5 seconds after capture to restore

            // Update UI
            if (socket) {
                emitUpdatedStatus(socket);
            }
        } catch (error) {
            logger.error(`Failed to force Light ${lightNum} OFF: ${error.message}`);
        }
    } else if (captureAction === 'on') {
        // Force ON for capture
        try {
            // Only send command if state is changing
            if (!currentStatus) {
                if (controlMode === 'direct') {
                    // Direct mode - set pin to high
                    await controlLight(lightNum, true, { mode: 'direct' });
                    logger.info(`Light ${lightNum} forced ON for capture (direct mode, was OFF)`);
                } else if (controlMode === 'toggle') {
                    // Toggle mode - send a pulse to toggle from OFF to ON
                    await controlLight(lightNum, true, { mode: 'toggle', pulseWidth });
                    logger.info(`Light ${lightNum} toggled for capture to turn ON (toggle mode, was OFF)`);
                }
            } else {
                logger.info(`Light ${lightNum} already ON for capture`);
            }

            // Schedule restoration to scheduled state after capture
            lightTimers.restore[lightNum - 1] = setTimeout(() => {
                logger.info(`Restoring Light ${lightNum} to scheduled state after capture`);
                setLightToScheduledState(lightNum, socket);
            }, 5000); // Wait 5 seconds after capture to restore

            // Update UI
            if (socket) {
                emitUpdatedStatus(socket);
            }
        } catch (error) {
            logger.error(`Failed to force Light ${lightNum} ON: ${error.message}`);
        }
    } else {
        // No action, leave light in current state
        logger.info(`No capture action for Light ${lightNum}, current state: ${currentStatus ? 'ON' : 'OFF'}`);
    }
}

/**
 * Handle all lights for capture
 * @param {Object} socket - Socket.io socket for sending updates
 */
async function handleLightsForCapture(socket) {
    logger.info('Handling lights for capture...');

    // Handle each light based on its force settings
    const promises = [];

    for (let lightNum = 1; lightNum <= 3; lightNum++) {
        if (systemState[`light${lightNum}Connected`]) {
            promises.push(handleLightForCapture(lightNum, socket));
        }
    }

    // Wait for all light operations to complete
    await Promise.all(promises);
}

/**
 * Initialize light schedules for all connected lights
 * @param {Object} socket - Socket.io socket for sending updates
 */
function initializeLightSchedules(socket) {
    logger.info('Initializing 24-hour light schedules for all connected lights...');

    // Initialize each connected light
    for (let lightNum = 1; lightNum <= 3; lightNum++) {
        if (systemState[`light${lightNum}Connected`]) {
            setLightToScheduledState(lightNum, socket);
        }
    }
}

/**
 * Timelapse worker function
 * @param {Object} socket - Socket.io socket for sending updates
 */
async function timelapseWorker(socket) {
    if (systemState.timelapseActive && !systemState.timelapsePaused) {
        logger.info("Timelapse worker capturing image...");

        try {
            // Handle lights for capture (apply force settings)
            await handleLightsForCapture(socket);

            // Wait a short time for lights to stabilize
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Capture the image (not manual)
            if (captureImageWithRetry(socket, 3, false)) {
                logger.info("Timelapse image captured successfully");

                // Record the time of this capture
                systemState.lastCaptureTime = Date.now();

                // Set next capture time
                systemState.nextCapture = systemState.lastCaptureTime + systemState.captureInterval * 60 * 1000;

                // Emit status updates
                emitUpdatedStatus(socket);

                // Emit timelapse capture event
                socket.emit('timelapseCapture', {
                    status: "captured",
                    timestamp: new Date().toISOString(),
                    timelapseImagesTaken: systemState.timelapseImagesTaken,
                    totalImagesTaken: systemState.imagesTaken
                });

                // Schedule next capture
                systemState.captureThread = setTimeout(() => timelapseWorker(socket), systemState.captureInterval * 60 * 1000);
            } else {
                logger.error("Failed to capture image, pausing timelapse");

                // Turn off all lights if capture failed
                await turnOffAllLights(socket);

                // Pause timelapse
                systemState.timelapsePaused = true;
                socket.emit('timelapseStatus', { status: "paused", error: "Camera error" });

                // Keep checking if paused
                setTimeout(() => timelapseWorker(socket), 1000);
            }
        } catch (error) {
            logger.error("Error in timelapse worker:", error);

            // Pause timelapse on error
            systemState.timelapsePaused = true;
            socket.emit('timelapseStatus', { status: "paused", error: "Internal error" });

            // Keep checking if paused
            setTimeout(() => timelapseWorker(socket), 1000);
        }
    } else if (systemState.timelapseActive && systemState.timelapsePaused) {
        // If paused, keep checking every second
        setTimeout(() => timelapseWorker(socket), 1000);
    }
}

/**
 * Turn off all lights and clear all timers
 * @param {Object} socket - Socket.io socket for sending updates
 */
async function turnOffAllLights(socket) {
    logger.info('Turning off all lights and clearing timers...');

    // Clear all timers
    for (let i = 0; i < 3; i++) {
        if (lightTimers.daily[i]) {
            clearTimeout(lightTimers.daily[i]);
            lightTimers.daily[i] = null;
        }

        if (lightTimers.restore[i]) {
            clearTimeout(lightTimers.restore[i]);
            lightTimers.restore[i] = null;
        }
    }

    // Turn off all connected lights
    const promises = [];

    for (let lightNum = 1; lightNum <= 3; lightNum++) {
        if (systemState[`light${lightNum}Connected`]) {
            const controlMode = systemState[`light${lightNum}ControlMode`] || 'direct';
            const pulseWidth = systemState[`light${lightNum}PulseWidth`] || 100;
            const currentStatus = systemState[`light${lightNum}Status`];

            if (controlMode === 'direct') {
                // Direct mode - set pin to low
                promises.push(controlLight(lightNum, false, { mode: 'direct' }));
                logger.info(`Turning off Light ${lightNum} (direct mode)`);
            } else if (controlMode === 'toggle' && currentStatus) {
                // Toggle mode - only send pulse if light is currently ON
                promises.push(controlLight(lightNum, true, { mode: 'toggle', pulseWidth }));
                logger.info(`Toggling Light ${lightNum} to OFF (toggle mode, was ON)`);
            } else {
                logger.info(`Light ${lightNum} already OFF or using toggle mode and status unknown`);
            }
        }
    }

    await Promise.all(promises);
    emitUpdatedStatus(socket);
}

module.exports = {
    timelapseWorker,
    turnOffAllLights,
    handleLightsForCapture,
    initializeLightSchedules
};