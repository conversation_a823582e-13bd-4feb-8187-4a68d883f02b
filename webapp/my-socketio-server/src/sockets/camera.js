const fs = require('fs');
const path = require('path');
const { IMAGES_DIR, PUBLIC_DIR } = require('./constants');
const { execSync } = require('child_process');
const systemState = require('./state');
const { logger } = require('../logger');

function initializeCamera() {
  const commands = [
    'pkill gvfsd-gphoto2',
    'gphoto2 --auto-detect',
    'gphoto2 --set-config /main/imgsettings/imageformat=0'
  ];

  let success = true;
  for (const cmd of commands) {
    try {
      const result = execSync(cmd, { stdio: 'pipe' });
      logger.info(`Command succeeded: ${cmd}`);
    } catch (error) {
      logger.error(`Command failed: ${cmd}\n`, error.stderr?.toString() || error);
      success = false;
    }
  }

  if (success) {
    logger.info('Camera initialized with JPEG-only mode.');
    systemState.cameraConnected = true; // or however you track camera connection
  } else {
    logger.error('Camera initialization failed.');
    systemState.cameraConnected = false;
  }
  return success;
}

function keepCameraAwake() {
    setInterval(() => {
      try {
        const result = execSync('gphoto2 --get-config /main/settings/datetime', { stdio: 'pipe' });
        logger.info('Camera keep-alive success');
      } catch (error) {
        logger.error('Camera not found during keep-alive!', error.stderr?.toString() || error);
        // Optionally, try re-initialize if needed
        // initializeCamera();
      }
    }, 180_000); // 180,000 ms = 3 minutes
  }

function checkCameraConnection() {
    logger.info("Running camera diagnostics...");
    try {
      // Test camera connection by running a simple command
      const result = execSync('gphoto2 --auto-detect', { stdio: 'pipe', timeout: 5000 });
      const output = result.toString();
      const isConnected = output.includes('usb:') || output.includes('Camera');

      // Update system state with actual camera status
      systemState.cameraConnected = isConnected;

      if (isConnected) {
          logger.info("Camera connected and ready");
      } else {
          logger.info("No camera detected during diagnostics");
      }

      return isConnected;
  } catch (error) {
    logger.error("Camera check failed:", {
        message: error.message,
        stderr: error.stderr ? error.stderr.toString() : undefined
    });
    systemState.cameraConnected = false;
    return false;
  }
}

function captureImageWithRetry(socket, maxRetries = 3, isManualCapture = false) {
    if (!checkCameraConnection()) {
      logger.info("Camera not connected!");
      return false;
    }

    // Make sure IMAGES_DIR exists
    if (!fs.existsSync(IMAGES_DIR)) {
      fs.mkdirSync(IMAGES_DIR, { recursive: true });
      logger.info(`Created images directory: ${IMAGES_DIR}`);
    }

    const captureDate = new Date();
    const timestamp = captureDate.toISOString().replace(/[:.]/g, '-');
    const formattedTimestamp = captureDate.toISOString();

    // Use different naming convention based on capture type
    const prefix = isManualCapture ? 'manual' : 'timelapse';
    const saveFile = path.join(IMAGES_DIR, `${prefix}-${timestamp}.jpg`);

    // Log the capture type
    logger.info(`Capturing ${isManualCapture ? 'manual' : 'timelapse'} image`);

    logger.info(`Attempting to capture image to: ${saveFile}`);

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        execSync(`gphoto2 --capture-image-and-download --filename=${saveFile} --force-overwrite`, {
          stdio: 'pipe',
          timeout: 30000, // 30 seconds
        });

        // Verify the file exists and has content
        if (fs.existsSync(saveFile) && fs.statSync(saveFile).size > 0) {
          logger.info(`Image captured successfully: ${saveFile}`);

          // Update the appropriate counter based on capture type
          systemState.imagesTaken += 1; // Always increment total count

          if (isManualCapture) {
            systemState.manualImagesTaken += 1;
            logger.info(`Manual capture - Counters: manual=${systemState.manualImagesTaken}, timelapse=${systemState.timelapseImagesTaken}, total=${systemState.imagesTaken}`);
          } else {
            systemState.timelapseImagesTaken += 1;
            logger.info(`Timelapse capture - Counters: manual=${systemState.manualImagesTaken}, timelapse=${systemState.timelapseImagesTaken}, total=${systemState.imagesTaken}`);
          }

          const publicImagesDir = path.join(PUBLIC_DIR, 'images');

          // Update first/last image paths
          const isFirstImage = !systemState.firstImagePath || (systemState.timelapseActive && systemState.imagesTaken === 1);

          if (isFirstImage) {
            systemState.firstImagePath = saveFile;
            systemState.firstImageTimestamp = formattedTimestamp;
            logger.info(`Setting first image: ${saveFile} with timestamp ${formattedTimestamp}`);
          }

          systemState.lastImagePath = saveFile;
          systemState.lastImageTimestamp = formattedTimestamp;
          logger.info(`Setting last image: ${saveFile} with timestamp ${formattedTimestamp}`);

          // Copy these images to a safe location that won't be deleted during zip operations
          if (!fs.existsSync(publicImagesDir)) {
              fs.mkdirSync(publicImagesDir, { recursive: true });
          }

          // Only update first.jpg if this is the first image
          if (isFirstImage) {
            fs.copyFileSync(saveFile, path.join(publicImagesDir, 'first.jpg'));
            logger.info(`Updated first.jpg with ${saveFile}`);
          }

          // Always update last.jpg with the most recent image
          fs.copyFileSync(saveFile, path.join(publicImagesDir, 'last.jpg'));
          logger.info(`Updated last.jpg with ${saveFile}`);

          // If socket is provided, emit updated status
          if (socket) {
              require('./utils').emitUpdatedStatus(socket);
          }

          return true;
        } else {
            logger.error(`Image file is missing or empty after capture attempt ${attempt}`);
        }
      } catch (err) {
        logger.error(`Capture failed (attempt ${attempt}/${maxRetries}):`, {
            message: err.message,
            stderr: err.stderr ? err.stderr.toString() : undefined
        });

        // Re-initialize camera and wait before retry
        logger.info('Reinitializing camera before retry...');
        initializeCamera();
        require('child_process').execSync('sleep 5');
      }
    }
    logger.error('Failed to capture image after all retry attempts.');
    return false;
  }

// function captureImageFromCamera(socket) {
//     if (!checkCameraConnection()) {
//         logger.info("Camera not connected!");
//         return false;
//     }

//     const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
//     const dummyImagePath = path.join(IMAGES_DIR, `capture_${timestamp}.jpg`);
//     fs.writeFileSync(dummyImagePath, '');
//     logger.info(`Image captured: ${dummyImagePath}`);
//     systemState.imagesTaken += 1; // Increment the images taken count
//     require('./utils').emitUpdatedStatus(socket); // Emit updated status after capturing an image
//     return true;
// }

function startVideoRecording(socket) {
    if (!checkCameraConnection()) {
        logger.info("Camera not connected, cannot start video recording");
        return false;
    }

    // Make sure VIDEOS_DIR exists
    const VIDEOS_DIR = path.join(IMAGES_DIR, '../videos');
    if (!fs.existsSync(VIDEOS_DIR)) {
        fs.mkdirSync(VIDEOS_DIR, { recursive: true });
        logger.info(`Created videos directory: ${VIDEOS_DIR}`);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const videoFile = path.join(VIDEOS_DIR, `video-${timestamp}.mp4`);

    logger.info(`Starting video recording to: ${videoFile}`);

    try {
        // Start video recording in the background
        // Note: This is a simplified example. In a real implementation, you would
        // need to handle the process more carefully, possibly using child_process.spawn
        // to manage the recording process over time.
        const cmd = `gphoto2 --capture-movie --filename=${videoFile}`;
        const videoProcess = require('child_process').spawn('sh', ['-c', cmd], {
            detached: true,
            stdio: 'ignore'
        });

        // Store the process ID so we can stop it later
        systemState.videoProcess = videoProcess;
        systemState.videoFile = videoFile;
        systemState.isRecording = true;

        logger.info(`Video recording started with PID: ${videoProcess.pid}`);
        return true;
    } catch (err) {
        logger.error('Failed to start video recording:', err);
        return false;
    }
}

function stopVideoRecording(socket) {
    logger.info("Stopping video recording");

    if (!systemState.isRecording || !systemState.videoProcess) {
        logger.info("No active video recording to stop");
        systemState.isRecording = false;
        return true;
    }

    try {
        // Kill the video recording process
        process.kill(-systemState.videoProcess.pid, 'SIGTERM');
        logger.info(`Killed video recording process with PID: ${systemState.videoProcess.pid}`);

        // Check if the video file exists and has content
        if (fs.existsSync(systemState.videoFile) && fs.statSync(systemState.videoFile).size > 0) {
            logger.info(`Video recorded successfully: ${systemState.videoFile}`);

            // Create a zip file with the video
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const zipFilename = `video_${timestamp}.zip`;
            const VIDEOS_DIR = path.join(IMAGES_DIR, '../videos');
            const ZIPS_DIR = path.join(IMAGES_DIR, '../zips');
            const zipFilePath = path.join(ZIPS_DIR, zipFilename);

            // Create the zip file
            const cmd = `zip -j "${zipFilePath}" "${systemState.videoFile}"`;
            require('child_process').execSync(cmd);

            logger.info(`Created zip file for video: ${zipFilename}`);

            // Notify the client that a zip file is available for download
            if (socket) {
                socket.emit('zipCreated', { zipFilename });
            }
        } else {
            logger.error(`Video file is missing or empty: ${systemState.videoFile}`);
        }

        // Reset recording state
        systemState.isRecording = false;
        systemState.videoProcess = null;
        systemState.videoFile = null;

        return true;
    } catch (err) {
        logger.error('Failed to stop video recording:', err);

        // Reset recording state even if there was an error
        systemState.isRecording = false;
        systemState.videoProcess = null;
        systemState.videoFile = null;

        return false;
    }
}

module.exports = {
    initializeCamera,
    keepCameraAwake,
    captureImageWithRetry,
    checkCameraConnection,
    // captureImageFromCamera,
    startVideoRecording,
    stopVideoRecording,
};