const fs = require('fs');
const path = require('path');

const CONFIG_FILE = path.join(__dirname, '..', 'config', 'lights.json');

// Load saved light configuration if available
let savedConfig = {
  light1Connected: true,
  light2Connected: false,
  light3Connected: false,
  light1ControlMode: 'direct',
  light2ControlMode: 'direct',
  light3ControlMode: 'direct',
  light1PulseWidth: 100,
  light2PulseWidth: 100,
  light3PulseWidth: 100,
  light1Status: false,
  light2Status: false,
  light3Status: false,
  light1StatusKnown: true,
  light2StatusKnown: true,
  light3StatusKnown: true
};

try {
  if (fs.existsSync(CONFIG_FILE)) {
    const loadedConfig = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
    // Merge the loaded config with the default config
    savedConfig = { ...savedConfig, ...loadedConfig };
    console.log('Loaded saved light configuration');
  }
} catch (err) {
  console.error('Error loading light configuration:', err);
}

const systemState = {
    light1Status: savedConfig.light1Status,
    light2Status: savedConfig.light2Status,
    light3Status: savedConfig.light3Status,
    light1Connected: savedConfig.light1Connected,
    light2Connected: savedConfig.light2Connected,
    light3Connected: savedConfig.light3Connected,
    light1ControlMode: savedConfig.light1ControlMode, // 'direct' or 'toggle'
    light2ControlMode: savedConfig.light2ControlMode,
    light3ControlMode: savedConfig.light3ControlMode,
    light1PulseWidth: savedConfig.light1PulseWidth, // milliseconds for toggle pulse
    light2PulseWidth: savedConfig.light2PulseWidth,
    light3PulseWidth: savedConfig.light3PulseWidth,
    light1StatusKnown: savedConfig.light1StatusKnown, // Whether we know the actual status (always true for direct mode)
    light2StatusKnown: savedConfig.light2StatusKnown,
    light3StatusKnown: savedConfig.light3StatusKnown,
    light1ForceOffAtCapture: false,
    light2ForceOffAtCapture: false,
    light3ForceOffAtCapture: false,
    light1ForceOnAtCapture: false,
    light2ForceOnAtCapture: false,
    light3ForceOnAtCapture: false,
    isRecording: false,
    videoProcess: null, // Store the video recording process
    videoFile: null, // Store the path to the current video file
    timelapseActive: false,
    timelapsePaused: false,
    captureInterval: 15, // minutes
    lightDuration: 12, // hours per day, default value
    lightStartHour: 0, // Hour of day when lights turn ON (0-23)
    cameraConnected: true,
    networkStatus: false,
    lastDiagnostic: null,
    nextCapture: null,
    captureThread: null,
    imagesTaken: 0, // Total number of images taken
    timelapseImagesTaken: 0, // Number of images taken during timelapse
    manualImagesTaken: 0, // Number of images taken manually
    firstImagePath: null, // Path to first image in current session
    lastImagePath: null, // Path to most recent image
    firstImageTimestamp: null, // Timestamp of first image
    lastImageTimestamp: null, // Timestamp of last image
    // DHT22 sensor data
    currentTemp: null, // Current temperature in Celsius
    currentRH: null, // Current relative humidity in %
    tempHistory: [], // Array of temperature readings (max 60 entries)
    rhHistory: [] // Array of humidity readings (max 60 entries)
};

// Function to save light configuration
function saveConfig() {
    try {
      const dir = path.dirname(CONFIG_FILE);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      const config = {
        light1Connected: systemState.light1Connected,
        light2Connected: systemState.light2Connected,
        light3Connected: systemState.light3Connected,
        light1ControlMode: systemState.light1ControlMode,
        light2ControlMode: systemState.light2ControlMode,
        light3ControlMode: systemState.light3ControlMode,
        light1PulseWidth: systemState.light1PulseWidth,
        light2PulseWidth: systemState.light2PulseWidth,
        light3PulseWidth: systemState.light3PulseWidth,
        light1Status: systemState.light1Status,
        light2Status: systemState.light2Status,
        light3Status: systemState.light3Status,
        light1StatusKnown: systemState.light1StatusKnown,
        light2StatusKnown: systemState.light2StatusKnown,
        light3StatusKnown: systemState.light3StatusKnown
      };
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    } catch (err) {
      console.error('Error saving light configuration:', err);
    }
}

module.exports = systemState;
module.exports.saveConfig = saveConfig;