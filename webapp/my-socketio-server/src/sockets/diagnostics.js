const systemState = require('./state');
const { checkCameraConnection } = require('./camera');
const { checkNetworkOnce } = require('./network');
const { getStorageInfo } = require('./storage');

function runSystemDiagnostics() {
    return {
        camera: checkCameraConnection(),
        network: checkNetworkOnce(),
        storage: getStorageInfo(),
        timestamp: new Date().toISOString(),
        // Include sensor data in diagnostics
        temperature: systemState.currentTemp,
        humidity: systemState.currentRH,
        tempHistory: systemState.tempHistory,
        rhHistory: systemState.rhHistory
    };
}

module.exports = {
    runSystemDiagnostics,
};