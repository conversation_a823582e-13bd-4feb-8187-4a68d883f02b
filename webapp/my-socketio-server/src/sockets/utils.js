const fs = require('fs');
const path = require('path');
const { logger } = require('../logger');
const { runSystemDiagnostics } = require('./diagnostics');
const { IMAGES_DIR, ZIPS_DIR } = require('./constants');
const systemState = require('./state');

function emitUpdatedStatus(socket) {
    const diagnostics = runSystemDiagnostics();

    const serverUrl = 'http://raspberrypi.local:3000'; // Use your server URL

    // Create URLs for the images
    const firstImageUrl = systemState.firstImagePath ?
        `${serverUrl}/images/first.jpg?t=${Date.now()}` : null;
    const lastImageUrl = systemState.lastImagePath ?
        `${serverUrl}/images/last.jpg?t=${Date.now()}` : null;

    // Get timestamps for the images
    const firstImageTimestamp = systemState.firstImageTimestamp || null;
    const lastImageTimestamp = systemState.lastImageTimestamp || null;

    logger.info('Emitting status with camera and light states:', {
        camera: diagnostics.camera,
        light1: systemState.light1Status,
        light2: systemState.light2Status,
        light3: systemState.light3Status
      });

    socket.emit('status', {
        isCameraConnected: diagnostics.camera,
        networkStatus: diagnostics.network,
        light1Status: systemState.light1Status,
        light2Status: systemState.light2Status,
        light3Status: systemState.light3Status,
        light1Connected: systemState.light1Connected,
        light2Connected: systemState.light2Connected,
        light3Connected: systemState.light3Connected,
        light1ControlMode: systemState.light1ControlMode,
        light2ControlMode: systemState.light2ControlMode,
        light3ControlMode: systemState.light3ControlMode,
        light1PulseWidth: systemState.light1PulseWidth,
        light2PulseWidth: systemState.light2PulseWidth,
        light3PulseWidth: systemState.light3PulseWidth,
        light1StatusKnown: systemState.light1StatusKnown,
        light2StatusKnown: systemState.light2StatusKnown,
        light3StatusKnown: systemState.light3StatusKnown,
        availableImages: fs.readdirSync(IMAGES_DIR).length,
        nextCapture: systemState.nextCapture ? new Date(systemState.nextCapture).toISOString() : null,
        storage: diagnostics.storage,
        lastDiagnostic: diagnostics.timestamp,
        zipFiles: fs.readdirSync(ZIPS_DIR).map(file => ({
            name: file,
            size: fs.statSync(path.join(ZIPS_DIR, file)).size,
            created: fs.statSync(path.join(ZIPS_DIR, file)).birthtime.toISOString(),
        })),
        timelapseActive: systemState.timelapseActive,
        timelapsePaused: systemState.timelapsePaused,
        firstImageUrl,
        lastImageUrl,
        firstImageTimestamp,
        lastImageTimestamp,
        imagesTaken: systemState.imagesTaken,
        timelapseImagesTaken: systemState.timelapseImagesTaken,
        manualImagesTaken: systemState.manualImagesTaken,
        // Include sensor data
        currentTemp: systemState.currentTemp,
        currentRH: systemState.currentRH,
        tempHistory: systemState.tempHistory,
        rhHistory: systemState.rhHistory
    });
}

function attemptReconnection() {
    logger.info("Attempting to re-establish network connection...");
    try {
        logger.info("Reconnection commands executed. Check again soon.");
    } catch (err) {
        logger.error("Error while trying to reconnect:", err.message);
    }
}

module.exports = {
    emitUpdatedStatus,
    attemptReconnection,
};