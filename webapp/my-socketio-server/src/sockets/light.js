const systemState = require('./state');
const { logger } = require('../logger');
const { setPin, PINS } = require('../hardware/rpi_gpio');

async function controlLight(lightNumber, status, options = {}) {
    const { mode = 'direct', pulseWidth = 100 } = options;

    if (mode === 'direct') {
        logger.info(`Light ${lightNumber} ${status ? 'ON' : 'OFF'} (direct mode)`);
    } else if (mode === 'toggle') {
        logger.info(`Light ${lightNumber} toggle pulse (${pulseWidth}ms)`);
    }

    let success = false;
    let pin;

    // Map light number to GPIO pin
    switch(lightNumber) {
        case 1:
            pin = PINS.LIGHT1;
            break;
        case 2:
            pin = PINS.LIGHT2;
            break;
        case 3:
            pin = PINS.LIGHT3;
            break;
        default:
            logger.error('Invalid light number:', lightNumber);
            return false;
    }

    // Control the pin based on mode
    if (mode === 'direct') {
        // Direct mode - set pin to high or low
        success = await setPin(pin, status, 'direct');

        if (success) {
            systemState[`light${lightNumber}Status`] = status;
            logger.info(`Status Changed: Light ${lightNumber} ${status ? 'ON' : 'OFF'} success`);
        }
    } else if (mode === 'toggle') {
        // Toggle mode - send a pulse to toggle the light
        success = await setPin(pin, true, 'toggle', pulseWidth);

        if (success) {
            // In toggle mode, we invert the stored status since we assume the light toggled
            const currentStatus = systemState[`light${lightNumber}Status`];
            systemState[`light${lightNumber}Status`] = !currentStatus;
            logger.info(`Status Changed (toggle): Light ${lightNumber} ${!currentStatus ? 'ON' : 'OFF'} (assumed)`);
        }
    }

    return success;
}

module.exports = {
    controlLight,
};