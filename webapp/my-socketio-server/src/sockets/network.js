const { execSync } = require('child_process');
const systemState = require('./state');
const { logger } = require('../logger');

let consecutiveFailures = 0;      // Track how many checks have failed in a row
const MAX_CONSECUTIVE_FAILURES = 5; // After 5 failures, we might slow down or do something else

let lastSuccessfulCheck = Date.now();
const CHECK_INTERVAL_MS = 300_000;  // 5 minutes, for example

function getBackoffDelay(consecutiveFailures) {
    const baseInterval = 300_000;   // 5 minutes
    const maxInterval = 3_600_000;  // 60 minutes
    const delay = baseInterval * Math.pow(2, consecutiveFailures - 1);
    return Math.min(delay, maxInterval);
}

function keepNetworkAlive() {
    function checkAndScheduleAgain() {
        const isOnline = checkNetworkOnce(); // returns true/false
        if (isOnline) {
            consecutiveFailures = 0;
            setTimeout(checkAndScheduleAgain, 300_000); // back to normal 5 min
        } else {
            consecutiveFailures++;
            const delay = getBackoffDelay(consecutiveFailures);
            setTimeout(checkAndScheduleAgain, delay);
        }
    }
    checkAndScheduleAgain(); // Start the loop
}

function checkNetworkOnce() {
  try {
    // 1-second timeout to quickly decide if network is up
    execSync('ping -c 1 -W 1 8.8.8.8', { stdio: 'ignore' });

    // If we get here, the ping succeeded
    systemState.networkStatus = true;
    consecutiveFailures = 0;
    lastSuccessfulCheck = Date.now();
    logger.info('Network is online.');
  } catch (err) {
    // The ping or command failed
    consecutiveFailures += 1;
    systemState.networkStatus = false;
    logger.warn(`Network check failed (attempt #${consecutiveFailures}): ${err.message}`);

    // Attempt reconnection if under the threshold
    if (consecutiveFailures <= MAX_CONSECUTIVE_FAILURES) {
      attemptReconnection();
    } else {
      // We’ve failed too many times consecutively.
      // You could implement a “backoff” or just keep logging.
      logger.error('Too many consecutive failures. Not attempting further immediate reconnects.');
    }
  }
}

function attemptReconnection() {
  logger.info('Attempting to re-establish network connection...');
  try {
    // For Raspberry Pi OS using dhcpcd:
    execSync('sudo systemctl restart dhcpcd', { stdio: 'pipe' });
    logger.info('Reconnection command executed successfully.');
  } catch (err) {
    logger.error(`Failed to restart dhcpcd: ${err.message}`);
    // If you have an alternative approach, try it here (e.g. ifdown/ifup, nmcli, etc.).
  }
}

module.exports = {
  keepNetworkAlive,
  checkNetworkOnce,
};