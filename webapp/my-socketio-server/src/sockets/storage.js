const fs = require('fs');
const path = require('path');
const { logger } = require('../logger');
const { execSync } = require('child_process');
const { IMAGES_DIR, ZIPS_DIR } = require('./constants');

function getStorageInfo() {
    try {
        const dfOutput = execSync(`df -k "${IMAGES_DIR}"`).toString().trim();
        const lines = dfOutput.split('\n');
        const parts = lines[1].split(/\s+/);

        const totalK = parseInt(parts[1], 10);
        const usedK = parseInt(parts[2], 10);

        const total = totalK * 1024;
        const used = usedK * 1024;
        const percentage = (used / total) * 100;

        return {
            total,
            used,
            percentage: Math.round(percentage * 10) / 10,
        };
    } catch (error) {
        logger.error('Error getting disk usage:', error);
        return { total: 0, used: 0, percentage: 0 };
    }
}

function createZipOfImages() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const zipFilename = `timelapse_${timestamp}.zip`;
    const zipFilePath = path.join(ZIPS_DIR, zipFilename);

    // The `-m` flag moves (deletes) original files after successfully adding them.
    try {
        const cmd = `zip -r -Tm "${zipFilePath}" .`;
        const output = execSync(cmd, { cwd: IMAGES_DIR, encoding: 'utf8' });
        logger.info(`Created zip: ${zipFilePath} and removed originals.`);
        return zipFilename;
    } catch (err) {
        logger.error('Error creating zip:', err);
        // The originals may or may not be partially removed in a failure scenario
    }
}

module.exports = {
    getStorageInfo,
    createZipOfImages,
};