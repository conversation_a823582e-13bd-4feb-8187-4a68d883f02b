const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Store reference to the socket.io instance
let io = null;

// Function to set the socket.io instance
function setIo(socketIo) {
    io = socketIo;
}

// Define log directory and create it if it doesn't exist
const LOG_DIR = '/home/<USER>/timelapse_dev/logs';
if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Custom format to safely handle circular references in objects
const safeStringify = (obj) => {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
      // Handle Error objects specially
      if (value instanceof Error) {
          return {
              message: value.message,
              stack: value.stack,
              ...(value.stderr && { stderr: value.stderr.toString() }),
              ...(value.stdout && { stdout: value.stdout.toString() })
          };
      }
      // Handle circular references
      if (typeof value === 'object' && value !== null) {
          if (seen.has(value)) {
              return '[Circular]';
          }
          seen.add(value);
      }
      return value;
  }, 2);
};

// Create a custom transport to emit logs to connected clients
class SocketTransport extends winston.Transport {
    constructor(opts) {
        super(opts);
        this.name = 'socket';
    }

    log(info, callback) {
        if (io) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                level: info.level,
                message: info.message
            };
            io.emit('serverLog', logEntry);
        }
        callback();
    }
}

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...rest }) => {
            let restString = '';
            if (Object.keys(rest).length) {
                try {
                    restString = safeStringify(rest);
                } catch (err) {
                    restString = `[Error serializing log data: ${err.message}]`;
                }
            }
            return `${timestamp} ${level}: ${message} ${restString}`;
          })
    ),
    transports: [
        // File transport
        new winston.transports.File({
            filename: path.join(LOG_DIR, 'timelapse.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            tailable: true
        }),
        // Console transport
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        }),
        // Socket transport for real-time logs
        new SocketTransport()
    ]
});

module.exports = {
    logger,
    setIo
};