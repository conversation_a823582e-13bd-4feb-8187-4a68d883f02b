const pigpio = require('pigpio');
const { logger } = require('../logger');
const Gpio = pigpio.Gpio;
const { LIGHT1_PIN, LIGHT2_PIN, LIGHT3_PIN } = require('./rpi_config');

// Initialize pigpio
pigpio.initialize();

let light1, light2, light3;

async function initializeGPIO() {
  try {
    // Initialize GPIO pins as outputs
    light1 = new Gpio(LIGHT1_PIN, {mode: Gpio.OUTPUT});
    light2 = new Gpio(LIGHT2_PIN, {mode: Gpio.OUTPUT});
    light3 = new Gpio(LIGHT3_PIN, {mode: Gpio.OUTPUT});

    // Initialize all lights to OFF
    light1.digitalWrite(0);
    light2.digitalWrite(0);
    light3.digitalWrite(0);

    logger.info('GPIO pins initialized successfully');
    logger.info('Using GPIO numbers:', {
      LIGHT1: LIGHT1_PIN,
      LIGHT2: LIGHT2_PIN,
      LIGHT3: LIGHT3_PIN
    });
    return true;
  } catch (error) {
    logger.error('Error initializing GPIO:', error);
    logger.error('Pin configuration:', {
      LIGHT1_PIN,
      LIGHT2_PIN,
      LIGHT3_PIN
    });
    return false;
  }
}

async function setPin(pin, state, mode = 'direct', pulseWidth = 100) {
  try {
    let gpioPin;

    // Map pin number to GPIO instance
    switch(pin) {
      case LIGHT1_PIN:
        gpioPin = light1;
        break;
      case LIGHT2_PIN:
        gpioPin = light2;
        break;
      case LIGHT3_PIN:
        gpioPin = light3;
        break;
      default:
        throw new Error(`Invalid pin number: ${pin}`);
    }

    if (mode === 'direct') {
      // Direct mode - set pin to high or low
      logger.info(`Setting GPIO${pin} to ${state} (direct mode)`);
      gpioPin.digitalWrite(state ? 1 : 0);
    } else if (mode === 'toggle') {
      // Toggle mode - send a pulse to toggle the light
      logger.info(`Sending toggle pulse to GPIO${pin} (${pulseWidth}ms)`);

      // Set pin high
      gpioPin.digitalWrite(1);

      // Wait for the specified pulse width
      await new Promise(resolve => setTimeout(resolve, pulseWidth));

      // Set pin low
      gpioPin.digitalWrite(0);

      logger.info(`Toggle pulse completed for GPIO${pin}`);
    }

    return true;
  } catch (error) {
    logger.error(`Error controlling GPIO${pin}:`, error);
    return false;
  }
}

function cleanup() {
  try {
    // Terminate pigpio
    pigpio.terminate();
    logger.info('GPIO pins cleaned up');
  } catch (error) {
    logger.error('Error during GPIO cleanup:', error);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  cleanup();
  process.exit(0);
});

module.exports = {
  initializeGPIO,
  setPin,
  cleanup,
  PINS: {
    LIGHT1: LIGHT1_PIN,
    LIGHT2: LIGHT2_PIN,
    LIGHT3: LIGHT3_PIN
  }
};