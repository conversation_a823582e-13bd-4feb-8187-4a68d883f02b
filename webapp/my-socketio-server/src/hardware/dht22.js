const sensor = require('node-dht-sensor');
const { logger } = require('../logger');
const systemState = require('../sockets/state');
const { emitUpdatedStatus } = require('../sockets/utils');

// DHT22 sensor configuration
const DHT_TYPE = 22; // DHT22 sensor type
const DHT_PIN = 24;  // GPIO24 pin
const MAX_HISTORY_LENGTH = 60; // Store last 60 readings (1 hour if reading every minute)
const READ_INTERVAL = 60000; // Read every minute (60000ms)

// Global variable to store the socket.io instance
let io = null;

/**
 * Initialize the DHT22 sensor
 * @returns {Promise<boolean>} Promise that resolves to success status
 */
function initializeDHT22() {
    return new Promise((resolve) => {
        try {
            logger.info(`Initializing DHT22 sensor on GPIO${DHT_PIN}`);
            const initialized = sensor.initialize(DHT_TYPE, DHT_PIN);

            if (initialized) {
                logger.info('DHT22 sensor initialized successfully');

                // Take an initial reading
                readSensor();

                // Set up periodic readings
                setInterval(readSensor, READ_INTERVAL);

                resolve(true);
            } else {
                logger.error('Failed to initialize DHT22 sensor');
                resolve(false);
            }
        } catch (error) {
            logger.error('Error initializing DHT22 sensor:', error);
            resolve(false);
        }
    });
}

/**
 * Read temperature and humidity from the DHT22 sensor
 */
function readSensor() {
    try {
        const reading = sensor.read();

        if (reading.isValid) {
            // Round temperature to 1 decimal place and humidity to integer
            const temperature = Math.round(reading.temperature * 10) / 10;
            const humidity = Math.round(reading.humidity);

            logger.info(`DHT22 reading: ${temperature}°C, ${humidity}%`);

            // Update current values
            systemState.currentTemp = temperature;
            systemState.currentRH = humidity;

            // Add to history arrays
            systemState.tempHistory.push(temperature);
            systemState.rhHistory.push(humidity);

            // Trim arrays if they exceed max length
            if (systemState.tempHistory.length > MAX_HISTORY_LENGTH) {
                systemState.tempHistory.shift();
            }
            if (systemState.rhHistory.length > MAX_HISTORY_LENGTH) {
                systemState.rhHistory.shift();
            }

            // Emit updated status to all connected clients if io is available
            if (io) {
                io.sockets.emit('sensorUpdate', {
                    temperature,
                    humidity,
                    tempHistory: systemState.tempHistory,
                    rhHistory: systemState.rhHistory
                });
            }
        } else {
            logger.warn('Invalid DHT22 reading');
        }
    } catch (error) {
        logger.error('Error reading from DHT22 sensor:', error);
    }
}

/**
 * Set the socket.io instance for real-time updates
 * @param {Object} socketIo - Socket.io instance
 */
function setIo(socketIo) {
    io = socketIo;
}

module.exports = {
    initializeDHT22,
    readSensor,
    setIo
};
