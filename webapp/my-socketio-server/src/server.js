const { app, server } = require('./app');
const { logger } = require('./logger');
const path = require('path');
const fs = require('fs');

const ZIPS_DIR = path.join(__dirname, 'zips');

// Serve zip files for download
app.get('/download/:filename', (req, res) => {
    const filename = req.params.filename;
    const filepath = path.join(ZIPS_DIR, filename);

    // Log the download request
    logger.info(`Download requested for: ${filename}`);

    // Check if file exists first
    if (!fs.existsSync(filepath)) {
        logger.error(`File not found: ${filepath}`);
        return res.status(404).send('File not found');
    }

    res.download(filepath, filename, (err) => {
        if (err) {
            logger.error('Error downloading file:', err);

            // Only send error if headers haven't been sent yet
            if (!res.headersSent) {
                res.status(500).send('Error downloading file');
            }
        } else {
            logger.info(`File downloaded successfully: ${filename}`);
        }
    });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    logger.info(`Server is running on port ${PORT}`);
});