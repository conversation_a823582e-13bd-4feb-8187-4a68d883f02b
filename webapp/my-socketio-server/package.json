{"name": "my-socketio-server", "version": "1.0.0", "description": "A basic Node.js server with Express and Socket.IO for real-time updates.", "main": "src/server.js", "scripts": {"start": "node src/server.js"}, "dependencies": {"archiver": "^7.0.1", "cors": "^2.8.5", "express": "^4.17.1", "node-dht-sensor": "^0.4.5", "pigpio": "^3.3.1", "socket.io": "^4.0.0", "winston": "^3.17.0"}, "author": "", "license": "ISC"}